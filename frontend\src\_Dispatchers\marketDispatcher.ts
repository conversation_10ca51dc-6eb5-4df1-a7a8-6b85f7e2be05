import { EventBus } from '../events/eventBus';
import { Symbol, KLineInterval, KLineData, KLine, MarketType, ExchangeType } from '@/shared_types/market';
import { marketService } from '../_Services/marketService';
import { MarketEvents } from '../events/events';
import { ShapeSignal, SIGNAL_DEFINITIONS } from '@/_Modules/Signal';
import { BaseSignal } from '@/_Modules/Signal/BaseSignal';
import { MACrossSignal } from '@/_Modules/Signal/signals/MACrossSignal';
import { RSISignal } from '@/_Modules/Signal/signals/RSISignal';
import { MACDSignal } from '@/_Modules/Signal/signals/MACDSignal';
import { BollingerBandsSignal } from '@/_Modules/Signal/signals/BollingerBandsSignal';
import { DonchianChannelSignal } from '@/_Modules/Signal/signals/DonchianChannelSignal';
import { VWAPDeviationSignal } from '@/_Modules/Signal/signals/VWAPDeviationSignal';
import { FutureReturnSignal } from '@/_Modules/Signal/signals/FutureReturnSignal';
import httpDispatcher from './HttpDispatcher';
import { getToken } from '@/utils/auth';
import { ChartEvents } from '../events/events';
import { Shape, ShapeConfigItem } from '@/shared_types/shape';

import { jotaiStore } from '@/store/state';
import { klineAtom } from '@/store/state';
import { v4 as uuidv4 } from 'uuid';
import { KLineCache as KLineCacheDB } from '@/utils/KLineCache';
import printCustomStack from '@/utils/stackUtils';
import { poolCache, PoolItem } from '@/utils/PoolCache';



// K线数据缓存
interface KLineCache {
  symbol: Symbol;
  interval: KLineInterval;
  data: KLine;  // 改为 KLine 类型
  timestamp: number;
}

// 初始化市场事件分发器
const marketDispatcher = {
  browseHistoryInitialized: false,
  initialized: false,
  klineSubscribed: false,
  klinesReadySubscribed: false,

  subscribedSymbolListEvents: false, // Flag to prevent duplicate subscriptions
  initialize() {
    if (this.initialized) {
      //console.log('[MarketDispatcher] 已经初始化，跳过');
      return;
    }


    // 将订阅初始化移到这里
    this.subscribeToKLineDataRequests();
    this.subscribeToSignalCalculation();
    this.subscribeToChartConfig();
    this.subscribeToShapeConfig();
    this.subscribeToRemoveOption();

    // 新增：订阅 KLINES_READY 事件
    if (!this.klinesReadySubscribed) {
        EventBus.on(MarketEvents.Types.KLINES_READY, this.handleKlinesReady.bind(this)); // 使用 bind(this) 确保 this 指向正确
        this.klinesReadySubscribed = true;
        console.log('[MarketDispatcher] KLINES_READY 事件已订阅');
    }

    // 初始化时清理过期缓存
    this.clearExpiredCache();

    this.initialized = true;
    console.log('[MarketDispatcher] 初始化完成');
  },

  // 清理过期缓存
  async clearExpiredCache() {
    try {
      await KLineCacheDB.clearExpired();
    } catch (error) {
      console.error('[MarketDispatcher] 清理过期缓存失败:', error);
    }
  },

  // 获取K线数据
  async fetchKLineData(
    symbol: Symbol,
    interval: KLineInterval,
    options: {
      startTime?: number;
      endTime?: number;
      limit?: number;
    } = {}
  ): Promise<KLineData[]> {
    try {

      const token = getToken();
      console.log('[MarketDispatcher] 开始获取K线数据，Token=', token);

      const response = await httpDispatcher.get('/market/klines', {
        params: {
          symbol: symbol.code,
          market: symbol.market,
          exchange: symbol.exchange,
          interval,
          ...options
        },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log(`[MarketDispatcher] K线数据响应状态码: ${response.status}`);
      console.log(`[MarketDispatcher] K线数据数量: ${response.data?.length || 0}`);

      if (response.data.success) {
        let klineData = response.data.data;

        // 检查数据是否按时间升序排列
        if (klineData.length > 1) {
          const isDescending = klineData[0].time > klineData[1].time;
          if (isDescending) {
            console.log('[MarketDispatcher] 检测到K线数据为倒序，正在转换为升序...');
            klineData = klineData.reverse();
          }
        }

        // 更新浏览历史列表 (修复参数数量错误)
        // 浏览历史需要排序，所以 needOrder 为 true
        await this.updateSymbolListItem('浏览历史', symbol, true, false); // 确认传递4个参数

        return klineData;
      }
      throw new Error(response.data.error || 'Failed to fetch kline data');
    } catch (error) {
      console.error('获取K线数据失败:', error);
      throw error;
    }
  },

  /**
   * 获取K线数据（不影响应用状态）
   * 这个函数是专门为多品种重叠指标设计的，不会影响应用状态
   * 使用与GET_KLINES相同的缓存逻辑，但不更新应用状态
   *
   * @param fullCode 完整的品种代码，格式为 xxx.xxx.xxx
   * @param interval K线周期
   * @param options 可选参数，包括开始时间、结束时间和数据限制
   * @returns 返回K线数据数组
   */
  async getKLines(
    fullCode: string | Symbol,
    interval: KLineInterval,
    options: {
      startTime?: number;
      endTime?: number;
      limit?: number;
    } = {}
  ): Promise<{ success: boolean; data?: KLineData[]; error?: string }> {
    try {
      // 构建Symbol对象
      let symbol: Symbol;

      if (typeof fullCode === 'string') {
        // 解析完整代码 xxx.xxx.xxx
        const parts = fullCode.split('.');
        if (parts.length !== 3) {
          throw new Error(`品种代码 ${fullCode} 格式不正确，应为 xxx.xxx.xxx`);
        }

        symbol = {
          code: parts[2],
          market: parts[1] as MarketType, // 类型转换
          exchange: parts[0] as ExchangeType, // 类型转换
          name: parts[2] // 名称默认使用代码
        };
      } else {
        // 已经是Symbol对象
        symbol = fullCode;
      }

      console.log(`[MarketDispatcher] 获取K线数据(外部指标): ${symbol.exchange}.${symbol.market}.${symbol.code}, 周期: ${interval}`);

      let klineData: KLineData[] = [];
      let usedCache = false;

      // 首先尝试从IndexedDB缓存中获取数据
      try {
        const cachedItem = await KLineCacheDB.get(symbol, interval);
        if (cachedItem) {
          console.log('[MarketDispatcher] 使用IndexedDB缓存的K线数据(外部指标)');

          // 获取缓存的K线数据
          const kline: KLine = cachedItem.data;

          console.log('[MarketDispatcher] 缓存的K线数据(外部指标):', kline);

          usedCache = true;
          klineData = kline.data;

          // 注意：这里不更新klineAtom，也不发送KLINES_READY事件
        }
      } catch (error) {
        console.error('[MarketDispatcher] 从IndexedDB获取缓存数据失败(外部指标):', error);
      }

      // 无论是否使用了缓存，都尝试从后端获取最新数据
      try {
        const backendKlineData = await this.fetchKLineData(symbol, interval, options);

        // 如果使用了缓存，需要比较后端数据是否比缓存数据更新
        if (usedCache && backendKlineData.length > 0) {
          const backendLastTime = backendKlineData[backendKlineData.length - 1].time;
          const cacheLastTime = klineData[klineData.length - 1].time;

          // 如果后端数据比缓存更新，则更新缓存
          if (backendLastTime > cacheLastTime) {
            console.log('[MarketDispatcher] 后端数据比缓存更新，更新缓存(外部指标)');

            // 创建新的KLine对象
            const updatedKline: KLine = {
              id: `${symbol.code}_${symbol.market}_${interval}`,
              period: interval,
              data: backendKlineData,
              symbol: symbol
            };

            // 更新IndexedDB缓存
            await KLineCacheDB.set(symbol, interval, updatedKline);

            console.log('[MarketDispatcher] K线数据已更新(外部指标)，数据长度:', backendKlineData.length);

            // 返回更新后的数据
            return {
              success: true,
              data: backendKlineData
            };
          } else {
            console.log('[MarketDispatcher] 缓存数据已是最新(外部指标)，无需更新');

            // 返回缓存数据
            return {
              success: true,
              data: klineData
            };
          }
        }
        // 如果没有使用缓存（缓存不存在），则直接使用后端数据
        else if (!usedCache) {
          console.log('[MarketDispatcher] 使用后端获取的K线数据(外部指标)');

          // 创建 KLine 对象
          const kline: KLine = {
            id: `${symbol.code}_${symbol.market}_${interval}`,
            period: interval,
            data: backendKlineData,
            symbol: symbol
          };

          // 更新IndexedDB缓存
          await KLineCacheDB.set(symbol, interval, kline);

          console.log('[MarketDispatcher] 后端获取的K线数据(外部指标):', kline);

          // 返回后端数据
          return {
            success: true,
            data: backendKlineData
          };
        }

        // 如果使用了缓存且缓存是最新的，返回缓存数据
        if (usedCache) {
          return {
            success: true,
            data: klineData
          };
        }
      } catch (error) {
        console.error('[MarketDispatcher] 从后端获取K线数据失败(外部指标):', error);

        // 如果使用了缓存，即使后端获取失败，也返回缓存数据
        if (usedCache) {
          return {
            success: true,
            data: klineData
          };
        }

        // 如果没有使用缓存，且后端获取失败，则返回错误
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }

      // 这里应该不会执行到，但为了类型安全，返回一个默认值
      return {
        success: false,
        error: '未知错误'
      };
    } catch (error) {
      console.error('[MarketDispatcher] 获取K线数据失败(外部指标):', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },

  /**
   * 修正周线数据
   * 检查周线最后一根K线是否需要修正，并用日线数据补齐缺少的周线数据
   * @param symbol 交易品种
   * @param weeklyKLine 周线数据
   * @returns 修正后的周线数据，如果无需修正或无法修正则返回原始数据
   */
  async fixWeeklyKLine(symbol: Symbol, weeklyKLine: KLine): Promise<KLine> {
    // 只处理周线数据
    if (weeklyKLine.period !== KLineInterval.WEEK1) {
      return weeklyKLine;
    }

    console.log('[MarketDispatcher] 开始周线修正...');

    try {
      // 尝试从本地缓存获取日线数据
      const cachedDailyItem = await KLineCacheDB.get(symbol, KLineInterval.DAY1);

      // 如果没有日线数据，则退出函数
      if (!cachedDailyItem) {
        console.log('[MarketDispatcher] 未找到日线数据，无法进行周线修正');
        return weeklyKLine;
      }

      const dailyKLine = cachedDailyItem.data;

      // 确保日线和周线数据都有数据
      if (!dailyKLine.data.length || !weeklyKLine.data.length) {
        console.log('[MarketDispatcher] 日线或周线数据为空，无法进行周线修正');
        return weeklyKLine;
      }

      // 获取日线和周线的最新时间
      const latestDailyTime = dailyKLine.data[dailyKLine.data.length - 1].time;
      const latestWeeklyTime = weeklyKLine.data[weeklyKLine.data.length - 1].time;

      console.log(`[MarketDispatcher] 日线最新时间: ${new Date(latestDailyTime * 1000).toISOString()}`);
      console.log(`[MarketDispatcher] 周线最新时间: ${new Date(latestWeeklyTime * 1000).toISOString()}`);

      // 第一步：确认是否需要修正
      // 如果日线最新数据不晚于周线最新数据，则无需修正
      if (latestDailyTime <= latestWeeklyTime) {
        console.log('[MarketDispatcher] 日线最新数据不晚于周线最新数据，无需修正');
        return weeklyKLine;
      }

      console.log('[MarketDispatcher] 日线最新数据晚于周线最新数据，需要进行周线修正');

      // 使用上海时区（UTC+8）来确定周的开始
      const SHANGHAI_OFFSET_MS = 8 * 60 * 60 * 1000; // UTC+8

      // 第二步：开始修正周线数据
      // 复制原有周线数据
      const newWeeklyData: KLineData[] = [...weeklyKLine.data];

      // 获取最后一周的开始时间
      const lastWeekStartTime = newWeeklyData[newWeeklyData.length - 1].time;
      const lastWeekDate = new Date(lastWeekStartTime * 1000 + SHANGHAI_OFFSET_MS);

      // 计算下一周的开始时间（周一）
      let nextWeekDate = new Date(lastWeekDate);
      // 找到下一个周一
      nextWeekDate.setUTCDate(lastWeekDate.getUTCDate() + (8 - lastWeekDate.getUTCDay()) % 7);
      if (nextWeekDate.getUTCDay() !== 1) {
        nextWeekDate.setUTCDate(nextWeekDate.getUTCDate() + (1 + 7 - nextWeekDate.getUTCDay()) % 7);
      }
      nextWeekDate.setUTCHours(0, 0, 0, 0);
      let nextWeekStartTime = Math.floor((nextWeekDate.getTime() - SHANGHAI_OFFSET_MS) / 1000);

      console.log(`[MarketDispatcher] 最后一周的开始时间: ${new Date(lastWeekStartTime * 1000).toISOString()}`);
      console.log(`[MarketDispatcher] 下一周的开始时间: ${new Date(nextWeekStartTime * 1000).toISOString()}`);

      // 1. 首先修正最后一根周线
      // 收集属于最后一周的日线数据（周一到周五，仅对比日期）
      let lastWeekDailyBars: KLineData[] = [];
      for (const dailyBar of dailyKLine.data) {
        if (dailyBar.time >= lastWeekStartTime && dailyBar.time < nextWeekStartTime) {
          lastWeekDailyBars.push(dailyBar);
        }
      }

      if (lastWeekDailyBars.length > 0) {
        console.log(`[MarketDispatcher] 找到 ${lastWeekDailyBars.length} 条属于最后一周的日线数据`);

        // 合并成为本周周线，更新掉最后一根周线
        const updatedLastWeekBar = this.aggregateWeeklyBar(lastWeekDailyBars, lastWeekStartTime);
        newWeeklyData[newWeeklyData.length - 1] = updatedLastWeekBar;

        console.log(`[MarketDispatcher] 已更新最后一周的周线数据，开盘价: ${updatedLastWeekBar.open}, 收盘价: ${updatedLastWeekBar.close}`);
      }

      // 2. 如果还有更多的日线，就步进到下一周时间范围，重复步骤1
      let currentWeekStartTime = nextWeekStartTime;
      let currentWeekDate = new Date(nextWeekDate);

      // 继续处理剩余的日线数据，直到没有更多日线
      while (true) {
        // 计算当前周的结束时间（下一周的开始时间）
        let nextWeekDate = new Date(currentWeekDate);
        nextWeekDate.setUTCDate(currentWeekDate.getUTCDate() + 7); // 直接加7天
        nextWeekDate.setUTCHours(0, 0, 0, 0);
        let nextWeekStartTime = Math.floor((nextWeekDate.getTime() - SHANGHAI_OFFSET_MS) / 1000);

        // 收集属于当前周的日线数据
        let currentWeekDailyBars: KLineData[] = [];
        for (const dailyBar of dailyKLine.data) {
          if (dailyBar.time >= currentWeekStartTime && dailyBar.time < nextWeekStartTime) {
            currentWeekDailyBars.push(dailyBar);
          }
        }

        // 如果没有找到属于当前周的日线数据，说明没有更多日线了，退出循环
        if (currentWeekDailyBars.length === 0) {
          break;
        }

        console.log(`[MarketDispatcher] 找到 ${currentWeekDailyBars.length} 条属于新周的日线数据，开始时间: ${new Date(currentWeekStartTime * 1000).toISOString()}`);

        // ====== 新增：去重检查 ======
        // 检查是否已经存在相同时间的周线数据
        const existingWeekIndex = newWeeklyData.findIndex(weekBar => weekBar.time === currentWeekStartTime);
        if (existingWeekIndex !== -1) {
          console.log(`[MarketDispatcher] 检测到重复的周线数据，时间: ${new Date(currentWeekStartTime * 1000).toISOString()}，跳过添加`);
          // 步进到下一周，继续检查
          currentWeekStartTime = nextWeekStartTime;
          currentWeekDate = new Date(nextWeekDate);
          continue;
        }

        // 合并成为新的周线，添加到周线数据中
        const newWeekBar = this.aggregateWeeklyBar(currentWeekDailyBars, currentWeekStartTime);
        newWeeklyData.push(newWeekBar);

        console.log(`[MarketDispatcher] 已添加新的周线数据，开始时间: ${new Date(currentWeekStartTime * 1000).toISOString()}, 开盘价: ${newWeekBar.open}, 收盘价: ${newWeekBar.close}`);

        // 步进到下一周
        currentWeekStartTime = nextWeekStartTime;
        currentWeekDate = new Date(nextWeekDate);
      }

      // ====== 新增：最终去重检查 ======
      // 检查整个数组是否有重复的时间戳
      const timeSet = new Set<number>();
      const uniqueWeeklyData: KLineData[] = [];
      
      for (const weekBar of newWeeklyData) {
        if (!timeSet.has(weekBar.time)) {
          timeSet.add(weekBar.time);
          uniqueWeeklyData.push(weekBar);
        } else {
          console.log(`[MarketDispatcher] 移除重复的周线数据，时间: ${new Date(weekBar.time * 1000).toISOString()}`);
        }
      }

      // 按时间排序
      uniqueWeeklyData.sort((a, b) => a.time - b.time);

      console.log(`[MarketDispatcher] 周线修正完成，原周线数据长度: ${weeklyKLine.data.length}，新周线数据长度: ${uniqueWeeklyData.length}，去重后长度: ${uniqueWeeklyData.length}`);

      // 创建修正后的周线对象
      const fixedWeeklyKLine: KLine = {
        ...weeklyKLine,
        data: uniqueWeeklyData
      };

      return fixedWeeklyKLine;
    } catch (error) {
      console.error('[MarketDispatcher] 周线修正失败:', error);
      return weeklyKLine; // 出错时返回原始数据
    }
  },

  /**
   * 将日线数据聚合为周线数据
   * @param dailyBars 一周的日线数据
   * @param weekStartTime 周开始时间
   * @returns 聚合后的周线数据
   */
  aggregateWeeklyBar(dailyBars: KLineData[], weekStartTime: number): KLineData {
    if (!dailyBars.length) {
      throw new Error('日线数据为空，无法聚合为周线');
    }

    // 使用第一条数据的开盘价作为周线开盘价
    const open = dailyBars[0].open;

    // 使用最后一条数据的收盘价作为周线收盘价
    const close = dailyBars[dailyBars.length - 1].close;

    // 找出最高价和最低价
    let high = dailyBars[0].high;
    let low = dailyBars[0].low;
    let volume = 0;

    for (const bar of dailyBars) {
      high = Math.max(high, bar.high);
      low = Math.min(low, bar.low);
      volume += bar.volume || 0;
    }

    // 创建周线数据
    const weeklyBar: KLineData = {
      time: weekStartTime,
      open,
      high,
      low,
      close,
      volume
    };

    return weeklyBar;
  },

  // 监听K线数据请求事件
  subscribeToKLineDataRequests(): void {
    if (this.klineSubscribed) {
      console.log('[MarketDispatcher] K线数据请求事件已订阅，跳过');
      return;
    }

    EventBus.on(MarketEvents.Types.GET_KLINES, async (payload: MarketEvents.GetKLines) => {
      try {
        const { symbol, interval, options } = payload;
        let klineData: KLineData[] = [];
        let usedCache = false;

        // 首先尝试从IndexedDB缓存中获取数据
        try {
          const cachedItem = await KLineCacheDB.get(symbol, interval);
          if (cachedItem) {
            console.log('[MarketDispatcher] 使用IndexedDB缓存的K线数据');

            // 创建 KLine 对象并更新 klineAtom
            const kline: KLine = cachedItem.data;

            // 更新 klineAtom
            jotaiStore.set(klineAtom, kline);

            console.log('[MarketDispatcher] 缓存的K线数据:', kline);

            // 创建K线对象
            let klineToEmit = {
              id: uuidv4(),
              data: kline.data,
              symbol,
              period: interval
            };

            // 如果是周线数据，尝试进行周线修正
            if (interval === KLineInterval.WEEK1) {
              console.log('[MarketDispatcher] 检测到缓存的周线数据，尝试进行周线修正');
              const fixedKline = await this.fixWeeklyKLine(symbol, klineToEmit);

              // 如果周线数据有修改，更新缓存
              if (fixedKline !== klineToEmit) {
                console.log('[MarketDispatcher] 缓存的周线数据已修正，更新缓存');
                klineToEmit = fixedKline;

                // 更新IndexedDB缓存
                await KLineCacheDB.set(symbol, interval, klineToEmit);

                // 更新klineAtom
                const updatedKline: KLine = {
                  ...kline,
                  data: fixedKline.data
                };
                jotaiStore.set(klineAtom, updatedKline);
              }
            }

            // 发送K线数据准备好的事件
            EventBus.emit(MarketEvents.Types.KLINES_READY, {
              kline: klineToEmit
            });

            console.log('[MarketDispatcher] 缓存的K线数据已准备就绪，数据长度:', klineToEmit.data.length);
            usedCache = true;
            klineData = klineToEmit.data;
          }
        } catch (error) {
          console.error('[MarketDispatcher] 从IndexedDB获取缓存数据失败:', error);
        }

        // 无论是否使用了缓存，都尝试从后端获取最新数据
        try {
          const backendKlineData = await this.fetchKLineData(symbol, interval, options);

          // 如果使用了缓存，需要比较后端数据是否比缓存数据更新
          if (usedCache && backendKlineData.length > 0) {
            const backendLastTime = backendKlineData[backendKlineData.length - 1].time;
            const cacheLastTime = klineData[klineData.length - 1].time;

            // 如果后端数据比缓存更新，则更新缓存并发送更新事件
            if (backendLastTime > cacheLastTime) {
              console.log('[MarketDispatcher] 后端数据比缓存更新，更新缓存');

              // 创建新的KLine对象
              let updatedKline: KLine = {
                id: `${symbol.code}_${symbol.market}_${interval}`,
                period: interval,
                data: backendKlineData,
                symbol: symbol
              };

              // 如果是周线数据，尝试进行周线修正
              if (interval === KLineInterval.WEEK1) {
                console.log('[MarketDispatcher] 检测到更新的周线数据，尝试进行周线修正');
                const fixedKline = await this.fixWeeklyKLine(symbol, updatedKline);

                // 如果周线数据有修改，使用修正后的数据
                if (fixedKline !== updatedKline) {
                  console.log('[MarketDispatcher] 更新的周线数据已修正');
                  updatedKline = fixedKline;
                }
              }

              // 更新 klineAtom
              jotaiStore.set(klineAtom, updatedKline);

              // 更新IndexedDB缓存
              await KLineCacheDB.set(symbol, interval, updatedKline);

              // 发送K线数据更新事件
              // 计算newDataIndex: 从缓存数据的长度开始
              const newDataIndex = klineData.length;

              EventBus.emit(MarketEvents.Types.KLINES_UPDATED, {
                kline: updatedKline,
                newDataIndex: newDataIndex
              });

              console.log('[MarketDispatcher] K线数据已更新，数据长度:', updatedKline.data.length);
            } else {
              console.log('[MarketDispatcher] 缓存数据已是最新，无需更新');
            }
          }
          // 如果没有使用缓存（缓存不存在），则直接使用后端数据
          else if (!usedCache) {
            console.log('[MarketDispatcher] 使用后端获取的K线数据');

            // 创建 KLine 对象
            const kline: KLine = {
              id: `${symbol.code}_${symbol.market}_${interval}`,
              period: interval,
              data: backendKlineData,
              symbol: symbol
            };

            // 更新 klineAtom
            jotaiStore.set(klineAtom, kline);

            // 更新IndexedDB缓存
            await KLineCacheDB.set(symbol, interval, kline);

            console.log('[MarketDispatcher] 后端获取的K线数据:', kline);

            // 创建K线对象
            let klineToEmit = {
              id: uuidv4(),
              data: backendKlineData,
              symbol,
              period: interval
            };

            // 如果是周线数据，尝试进行周线修正
            if (interval === KLineInterval.WEEK1) {
              console.log('[MarketDispatcher] 检测到周线数据，尝试进行周线修正');
              const fixedKline = await this.fixWeeklyKLine(symbol, klineToEmit);

              // 如果周线数据有修改，更新缓存
              if (fixedKline !== klineToEmit) {
                console.log('[MarketDispatcher] 周线数据已修正，更新缓存');
                klineToEmit = fixedKline;

                // 更新IndexedDB缓存
                await KLineCacheDB.set(symbol, interval, klineToEmit);
              }
            }

            // 发送K线数据准备好的事件
            EventBus.emit(MarketEvents.Types.KLINES_READY, {
              kline: klineToEmit
            });

            console.log('[MarketDispatcher] K线数据已准备就绪，数据长度:', klineToEmit.data.length);
          }
        } catch (error) {
          console.error('[MarketDispatcher] 从后端获取K线数据失败:', error);

          // 如果没有使用缓存，且后端获取失败，则发送错误事件
          if (!usedCache) {
            const errorPayload: MarketEvents.KLinesError = {
              error,
              symbol: payload.symbol,
              interval: payload.interval
            };
            EventBus.emit(MarketEvents.Types.KLINES_ERROR, errorPayload);
          }
        }
      } catch (error) {
        console.error('Get K-lines error:', error);
        // 发送错误事件
        const errorPayload: MarketEvents.KLinesError = {
          error,
          symbol: payload.symbol,
          interval: payload.interval
        };
        EventBus.emit(MarketEvents.Types.KLINES_ERROR, errorPayload);
      }
    });

    // 监听K线数据更新事件
    EventBus.on(MarketEvents.Types.KLINES_UPDATED, (payload: MarketEvents.KLinesUpdated) => {

        // 更新 klineAtom
        jotaiStore.set(klineAtom, payload.kline);

        // 同时更新IndexedDB缓存
        KLineCacheDB.set(payload.kline.symbol, payload.kline.period, payload.kline)
          .catch(error => console.error('[MarketDispatcher] 更新IndexedDB缓存失败:', error));

    });

    this.klineSubscribed = true;
  },

  // 搜索交易品种（跨市场搜索）
  searchSymbols: async (keyword: string) => {
    try {
      // 发送搜索开始事件
      const searchPayload: MarketEvents.SearchSymbols = { keyword };
      EventBus.emit(MarketEvents.Types.SEARCH_SYMBOLS, searchPayload);

      // 调用API搜索
      const data = await marketService.getSymbols(keyword);

      // 发送搜索结果事件
      const resultPayload: MarketEvents.SearchResult = { data, keyword };
      EventBus.emit(MarketEvents.Types.SEARCH_RESULT, resultPayload);

      return data;
    } catch (error) {
      console.error('Search symbols error:', error);
      // 发送错误事件
      const errorPayload: MarketEvents.SearchError = { error, keyword };
      EventBus.emit(MarketEvents.Types.SEARCH_ERROR, errorPayload);
      throw error;
    }
  },

  // 处理信号计算的函数
  async handleCalculateSignals(payload: MarketEvents.CalculateSignals) {
    try {
      console.log('[信号计算] <handleCalculateSignals> on CalculateSignals，计算中', payload);

      const { symbol, signalConfig } = payload;
      const currentKline = jotaiStore.get(klineAtom);

      // 检查当前K线数据是否可用
      if (!currentKline ||
        currentKline.symbol.code !== symbol.code ||
        currentKline.symbol.market !== symbol.market) {
        console.error('No K-line data available for signal calculation');
        return;
      }

      // 获取对应的信号定义
      const signalDef = SIGNAL_DEFINITIONS.find(s => s.name === signalConfig.name);
      if (!signalDef) {
        console.error(`Signal ${signalConfig.name} not found`);
        return;
      }

      console.log('[信号计算] 计算信号中，signalDef.signalClassName', signalDef.signalClassName);

      // 根据信号类名创建对应的信号实例
      let signalInstance: BaseSignal;
      let title = '';

      switch (signalDef.signalClassName) {
        case 'MACrossSignal':
          signalInstance = new MACrossSignal(signalConfig);
          title = '均线交叉'
          break;
        case 'RSISignal':
          signalInstance = new RSISignal(signalConfig);
          title = 'RSI超买超卖'
          break;
        case 'MACDSignal':
          signalInstance = new MACDSignal(signalConfig);
          title = 'MACD'
          break;
        case 'BollingerBandsSignal':
          signalInstance = new BollingerBandsSignal(signalConfig);
          title = '布林带信号'
          break;
        case 'DonchianChannelSignal':
          signalInstance = new DonchianChannelSignal(signalConfig);
          title = '唐奇安通道'
          break;
        case 'VWAPDeviationSignal':
          signalInstance = new VWAPDeviationSignal(signalConfig);
          title = 'VWAP偏差'
          break;
        case 'FutureReturnSignal':

          console.log('[信号计算] FutureReturnSignal', signalConfig);

          signalInstance = new FutureReturnSignal(signalConfig);
          title = '未来涨幅';
          break;
        case 'ShapeSignal':
          console.log('[信号计算] 构建 ShapeSignal 实例', signalConfig);
          signalInstance = new ShapeSignal(signalConfig);
          title = signalConfig.name;
          break;
        default:
          console.error(`[信号计算] Unknown signal class: ${signalConfig.name}`);
          return;
      }

      console.log('[信号计算] 计算信号 k线数据：', currentKline);
      // 使用当前K线数据
      const signals = signalInstance.calculateSignals(currentKline, signalConfig.parameters);
      console.log('[信号计算] 计算信号', signalInstance, ' 结果：', signals);

      // 发送计算结果
      if (signals && signals.length > 0) {
        EventBus.emit(MarketEvents.Types.SIGNALCALCULATED_RESULT, { title: title, signals: signals });
      }
    } catch (error) {
      // 只打印错误日志，不再发送错误事件
      console.error('Calculate signals error:', error);
    }
  },

  // 监听信号计算请求
  subscribeToSignalCalculation() {
    EventBus.on(MarketEvents.Types.CALCULATE_SIGNALS, this.handleCalculateSignals);
  },

  // 监听信号计算结果
  onSignalsResult: (callback: (payload: MarketEvents.SignalsCalculatedResultPayload) => void) => {
    return EventBus.on(MarketEvents.Types.SIGNALCALCULATED_RESULT, callback);
  },

  // 监听信号计算错误
  onSignalsError: (callback: (payload: MarketEvents.SignalsError) => void) => {
    return EventBus.on(MarketEvents.Types.SIGNALS_ERROR, callback);
  },

  // 监听图表配置保存事件
  subscribeToChartConfig() {
    EventBus.on(ChartEvents.Types.SAVE_CHART_CONFIG, async (payload: ChartEvents.SaveChartConfigPayload) => {
      try {
        // 保存到 localStorage
        localStorage.setItem('chartConfig', JSON.stringify(payload.config));
        console.log('[MarketDispatcher] 图表配置保存成功:', payload.config);
      } catch (error) {
        console.error('[MarketDispatcher] 保存图表配置失败:', error);
      }
    });
  },

  async loadShapeConfigs() {
    const token = getToken();
    const response = await httpDispatcher.get('/strategy/shapes', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('[形态] 获取形态配置:', response.data.data);

    return response.data.data;
  },

  // 保存形态配置到后端
  async saveShapeConfig(shapeConfigs: ShapeConfigItem[], name: string, klines: KLineData[]) {
    const token = getToken();
    try {
      // 对SHAPE指标进行特殊处理
      const processedShapeDetails = shapeConfigs.map(config => {
        if (config.type === 'SHAPE') {
          // 对于SHAPE指标，直接保存1-5序列，不需要转换
          console.log('[MarketDispatcher] SHAPE指标1-5序列:', config.values);
          
          return {
            indicatorType: config.type,
            indicatorParam: config.params,
            lineName: config.selectedLine,
            values: config.values, // 直接保存1-5序列
            weight: config.weight
          };
        } else {
          // 对于普通指标，保持原有的归一化值
          return {
            indicatorType: config.type,
            indicatorParam: config.params,
            lineName: config.selectedLine,
            values: config.values,
            weight: config.weight
          };
        }
      });

      const response = await httpDispatcher.post('/strategy/shapes', {
        name,
        klines: klines,
        shapeDetails: processedShapeDetails
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        console.log('[MarketDispatcher] 形态配置保存成功:', response);
        return response;
      } else {
        throw new Error(response.data.error || '保存形态配置失败');
      }
    } catch (error) {
      console.error('[MarketDispatcher] 保存形态配置失败:', error);
      throw error;
    }
  },

  // 监听形态配置保存事件
  subscribeToShapeConfig() {
    EventBus.on(MarketEvents.Types.SAVE_SHAPECONFIGS, async (payload: MarketEvents.SaveShapeConfig) => {
      try {
        const result = await this.saveShapeConfig(payload.shapeConfig.shapeConfigItems, payload.shapeConfig.name, payload.shapeConfig.klineData);
        // 发送保存成功事件
        EventBus.emit(MarketEvents.Types.SAVE_SHAPECONFIGS_RESULT, {
          success: true,
          data: result
        });

      } catch (error) {
        // 发送保存失败事件
        EventBus.emit(MarketEvents.Types.SAVE_SHAPECONFIGS_RESULT, {
          success: false,
          data: null,
          error: error instanceof Error ? error.message : '保存形态配置失败'
        });
      }
    });

    EventBus.on(MarketEvents.Types.GET_ALLSHAPECONFIGS, async (payload: MarketEvents.GetShapeConfigs) => {
      try {

        const userShapes = await this.loadShapeConfigs();

        // 通过callback返回结果
        if (payload.callback) {
          payload.callback(userShapes);
        }
      } catch (error) {
        console.error('获取形态配置失败:', error);
        if (payload.callback) {
          payload.callback([]);
        }
      }
    });

    // 监听获取形态详细信息事件
    EventBus.on(MarketEvents.Types.GET_SHAPECONFIGDETAILS, async (payload: MarketEvents.GetShapeConfigDetailsPayload) => {
      try {
        const token = getToken();
        const response = await httpDispatcher.get('/strategy/shape/details', {
          params: {
            name: payload.name
          },
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.data.success) {
          console.log('获取形态详细信息成功:', response.data.data);

          // 直接返回数据，因为格式已经正确
          payload.callback(response.data.data);
        } else {
          throw new Error(response.data.error || '获取形态详细信息失败');
        }
      } catch (error) {
        console.error('[MarketDispatcher] 获取形态详细信息失败:', error);
        payload.callback([]);
      }
    });
  },

  /**
   * 计算指定时间范围内的K线数量
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 返回K线数量和对应的索引
   */
  calculateKLineCount(startTime: number, endTime: number): { count: number; startIndex: number; endIndex: number } {
    const currentKline = jotaiStore.get(klineAtom);
    if (!currentKline || !currentKline.data.length) {
      console.error('没有可用的K线数据');
      return { count: 0, startIndex: -1, endIndex: -1 };
    }

    // 找到开始和结束时间对应的索引
    const startIndex = currentKline.data.findIndex(kline => kline.time >= startTime);
    const endIndex = currentKline.data.findIndex(kline => kline.time > endTime) - 1;

    const finalStartIndex = startIndex === -1 ? currentKline.data.length - 1 : startIndex;
    const finalEndIndex = endIndex === -2 ? currentKline.data.length - 1 : endIndex;

    const count = Math.abs(finalEndIndex - finalStartIndex) + 1;

    const result = {
      count,
      startIndex: Math.min(finalStartIndex, finalEndIndex),
      endIndex: Math.max(finalStartIndex, finalEndIndex)
    };

    console.log(`在时间范围 [${startTime}, ${endTime}] 内的 K线数量: ${count}, 开始索引: ${result.startIndex}, 结束索引: ${result.endIndex}`);
    return result;
  },


  convertSymbolToCode(symbol: Symbol) {
    // 输入参数是Symbol类型，输出 交易所.品类.代码
    return symbol.exchange + '.' + symbol.market + '.' + symbol.code;
  },

  // 更新指定名称的品种列表（使用 PoolCache）
  async updateSymbolListItem(symbolListName: string, symbol: Symbol, needOrder: boolean, isSystemList: boolean) {
    console.log(`[MarketDispatcher] 请求更新列表项: ${symbolListName}, 品种: ${symbol.code}`);

    try {
      // 确保 poolCache 已初始化
      await poolCache.initialize();

      // 使用 poolCache 获取当前列表数据
      let currentItems: PoolItem[] = [];
      try {
        currentItems = await poolCache.getPoolItems(symbolListName, isSystemList);
        console.log(`[MarketDispatcher] 获取到列表 ${symbolListName} 的当前数据，共 ${currentItems.length} 项`);
      } catch (error) {
        console.warn(`[MarketDispatcher] 获取列表 ${symbolListName} 失败，创建新列表:`, error);
      }

      // 创建新的列表项
      const symbolCodeString = this.convertSymbolToCode(symbol);
      console.log(`[MarketDispatcher] 转换后的品种代码: ${symbolCodeString}`);

      const newItem: PoolItem = {
        symbol: symbolCodeString,
        name: symbol.name || symbol.code,
        data: {}
      };

      // 如果需要排序，添加 viewedAt
      if (needOrder) {
        newItem.data = {
          viewedAt: new Date().toISOString()
        };
      }

      console.log(`[MarketDispatcher] 创建的新列表项:`, newItem);

      // 过滤掉已存在的相同品种
      const filteredItems = currentItems.filter(item => item.symbol !== symbolCodeString);
      console.log(`[MarketDispatcher] 过滤后的列表项数量: ${filteredItems.length}，过滤掉 ${currentItems.length - filteredItems.length} 项`);

      // 根据 needOrder 决定添加位置
      let updatedItems;
      if (needOrder) {
        updatedItems = [newItem, ...filteredItems];
        console.log(`[MarketDispatcher] 将新项添加到列表开头`);
      } else {
        updatedItems = [...filteredItems, newItem];
        console.log(`[MarketDispatcher] 将新项添加到列表末尾`);
      }

      // 限制列表大小
      const MAX_LIST_SIZE = 100;
      if (updatedItems.length > MAX_LIST_SIZE) {
        updatedItems = updatedItems.slice(0, MAX_LIST_SIZE);
        console.log(`[MarketDispatcher] 列表超过最大限制，截取前 ${MAX_LIST_SIZE} 项`);
      }

      // 使用 poolCache 更新列表
      console.log(`[MarketDispatcher] 准备更新列表 ${symbolListName}，共 ${updatedItems.length} 项`);
      await poolCache.updatePoolItems(symbolListName, isSystemList, updatedItems);

      // 强制立即同步到后端
      console.log(`[MarketDispatcher] 强制同步列表 ${symbolListName} 到后端`);
      await poolCache.syncPoolToBackend(symbolListName, isSystemList);

      // 发布列表已更新事件，通知相关组件更新数据
      console.log(`[MarketDispatcher] 发布 SYMBOLLIST_UPDATED 事件: ${symbolListName}`);
      EventBus.emit(MarketEvents.Types.SYMBOLLIST_UPDATED, {
        listName: symbolListName,
        isSystemList: isSystemList,
        theList: updatedItems
      });

      console.log(`[MarketDispatcher] 列表 ${symbolListName} 更新完成`);
    } catch (error) {
      console.error(`[MarketDispatcher] 更新列表 ${symbolListName} 失败:`, error);
    }
  },

  // 监听删除选项事件
  subscribeToRemoveOption() {
    EventBus.on(MarketEvents.Types.SIGNAL_REMOVEOPTION, async (payload: { paramName: string; option: string }) => {
      try {
        const token = getToken();
        // 只处理 shapeName 参数的删除
        if (payload.paramName === 'shapeName') {
          const response = await httpDispatcher.delete('/strategy/shapes', {
            params: {
              name: payload.option
            },
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (response.data.success) {
            console.log('[shape config] 形态删除成功:', payload.option);
            // 发送删除成功事件
            EventBus.emit(MarketEvents.Types.SIGNAL_OPTIONREMOVED, {
              option: payload.option,
              paramName: payload.paramName
            });
          } else {
            console.error('[MarketDispatcher] 形态删除失败:', response.data.error);
          }
        }
      } catch (error) {
        console.error('[MarketDispatcher] 处理删除选项事件失败:', error);
      }
    });
  },

  /**
   * 处理 KLinesReady 事件
   * @param payload 事件负载，包含K线数据和品种信息
   */
  async handleKlinesReady(payload: MarketEvents.KLinesReady) {
    const { symbol } = payload.kline;
    console.log('[MarketDispatcher] 收到 KLINES_READY 事件，准备更新浏览历史:', symbol);
    await this.addSymbolToHistory(symbol);
  },

  /**
   * 将品种添加到浏览历史记录中
   * @param symbol 要添加的品种对象
   */
  async addSymbolToHistory(symbol: Symbol) {
    console.log('[MarketDispatcher] 调用 addSymbolToHistory 更新浏览历史:', symbol);

    // 浏览历史需要排序 (needOrder=true), 并且是用户列表 (isSystemList=false)
    await this.updateSymbolListItem('浏览历史', symbol, true, false);

    console.log('[MarketDispatcher] 浏览历史更新完成 for symbol:', symbol.code);
  },
};

  // --- 新增：订阅列表相关事件 (Minimal version) ---


marketDispatcher.initialize();

// 获取用户形态设置
export const fetchUserShapes = async (userId: number) => {
  try {
    const token = getToken();
    const response = await httpDispatcher.get('/market/shapes', {
      params: {
        userId
      },
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.data.success) {
      return response.data.data; // 返回用户的形态设置
    }
    throw new Error(response.data.error || 'Failed to fetch user shapes');
  } catch (error) {
    console.error('获取用户形态设置失败:', error);
    throw error;
  }
};

// 获取指定用户、指定shapeId的详细配置
export const fetchShapeDetails = async (shapeId: number) => {
  try {
    const token = getToken();
    const response = await httpDispatcher.get(`/market/shapes/${shapeId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.data.success) {
      return response.data.data; // 返回指定shapeId的详细配置
    }
    throw new Error(response.data.error || 'Failed to fetch shape details');
  } catch (error) {
    console.error('获取形态详细配置失败:', error);
    throw error;
  }
};

// 保存形态设置
export const saveShapeSetting = async (name: string, userId: number, shapeDetails: any[]) => {
  try {
    const token = getToken();
    const response = await httpDispatcher.get('/market/shapes', {
      params: {
        userId
      },
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // 指定 userShapes 的类型
    const userShapes: Shape[] = response.data.data;

    // 检查形态名称是否已存在
    const existingShape = userShapes.find(shape => shape.name === name);
    if (existingShape) {
      throw new Error(`Shape with name "${name}" already exists.`);
    }

    // 调用 API 保存形态配置
    const saveResponse = await httpDispatcher.post('/market/shapes', {
      name,
      userId,
      shapeDetails
    }, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (saveResponse.data.success) {
      return saveResponse.data.data; // 返回保存成功的形态信息
    }
    throw new Error(saveResponse.data.error || 'Failed to save shape configuration');
  } catch (error) {
    console.error('保存形态设置失败:', error);
    throw error;
  }
};


export default marketDispatcher;
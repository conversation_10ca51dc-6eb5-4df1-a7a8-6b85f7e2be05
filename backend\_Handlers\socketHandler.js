/**
 * 简化的Socket.IO服务器处理器
 * 使用单端口、命名空间和房间机制实现用户隔离
 * 保持原有API接口不变，确保其他代码无需修改
 */

const express = require('express');
const router = express.Router();
const http = require('http');
const socketIo = require('socket.io');
const { authenticateToken } = require('../middleware/auth');
const config = require('../config.json');
const { marketHandler } = require('./marketHandler');
const tradeHandler = require('./tradeHandler');

// 创建简单的日志记录器
const logger = {
    info: (message) => console.log(`[SocketHandler INFO] ${message}`),
    warn: (message) => console.warn(`[SocketHandler WARN] ${message}`),
    error: (message) => console.error(`[SocketHandler ERROR] ${message}`),
    debug: (message) => console.log(`[SocketHandler DEBUG] ${message}`)
};

// 命名空间定义
const NAMESPACES = {
    REALTIME: '/realtime',
    TRADE: '/trade',
    STOCK_PROGRESS: '/stock_progress'
};

// 单端口Socket.IO配置
const SOCKET_CONFIG = {
    port: config.socket_port || 18888,
    cors: {
        origin: "*", // 生产环境应配置具体来源
        methods: ["GET", "POST"]
    }
};

// 全局Socket.IO服务器实例
let globalServer = null;
let globalIo = null;
let isServerRunning = false;

// 用户连接管理
const userConnections = new Map(); // username -> {port: fixed_port, connections: Set<socketId>}

/**
 * 启动全局Socket.IO服务器
 */
function startGlobalSocketServer() {
    if (isServerRunning) {
        logger.info('Socket.IO服务器已在运行');
        return true;
    }

    try {
        // 创建HTTP服务器
        globalServer = http.createServer((req, res) => {
            res.writeHead(200, { 'Content-Type': 'text/plain' });
            res.end('Socket.IO Server Running');
        });

        // 创建Socket.IO服务器
        globalIo = socketIo(globalServer, SOCKET_CONFIG);

        // 初始化命名空间
        initNamespaces();

        // 启动服务器
        globalServer.listen(SOCKET_CONFIG.port, () => {
            logger.info(`Socket.IO服务器已启动，端口: ${SOCKET_CONFIG.port}`);
            isServerRunning = true;
        });

        // 错误处理
        globalServer.on('error', (error) => {
            logger.error(`Socket.IO服务器错误: ${error.message}`);
            isServerRunning = false;
        });

        return true;
    } catch (error) {
        logger.error(`启动Socket.IO服务器失败: ${error.message}`);
        isServerRunning = false;
        return false;
    }
}

/**
 * 初始化命名空间
 */
function initNamespaces() {
    // 1. 创建实时数据命名空间 (/realtime)
    const realtimeNamespace = globalIo.of(NAMESPACES.REALTIME);
    logger.info(`创建命名空间: ${NAMESPACES.REALTIME} (实时K线数据处理)`);

    // 使用 marketHandler 的 initSocketIO 方法初始化实时数据命名空间
    if (marketHandler && typeof marketHandler.initSocketIO === 'function') {
        marketHandler.initSocketIO(realtimeNamespace);
        logger.info(`实时数据处理器Socket.IO服务初始化完成`);
    } else {
        logger.warn(`实时数据处理器Socket.IO服务初始化失败: marketHandler.initSocketIO 不是一个函数`);
    }

    // 2. 创建交易命名空间 (/trade)
    const tradeNamespace = globalIo.of(NAMESPACES.TRADE);
    logger.info(`创建命名空间: ${NAMESPACES.TRADE} (交易处理)`);

    // 初始化交易处理器的Socket.IO服务
    const tradeHandler = require('./tradeHandler/index');
    tradeHandler.initSocketIO(tradeNamespace);
    logger.info(`交易处理器Socket.IO服务初始化完成`);

    // 3. 创建选股进度命名空间 (/stock_progress)
    const stockProgressNamespace = globalIo.of(NAMESPACES.STOCK_PROGRESS);
    logger.info(`创建命名空间: ${NAMESPACES.STOCK_PROGRESS} (选股进度通信)`);

    // 初始化选股进度命名空间
    stockProgressNamespace.on('connection', (socket) => {
        logger.info(`[DEBUG] 选股进度命名空间收到新连接: ${socket.id}`);
        logger.info(`[DEBUG] 当前命名空间连接数: ${stockProgressNamespace.sockets.size}`);

        // 监听客户端订阅选股进度
        socket.on('subscribe_task', (taskId) => {
            logger.info(`[DEBUG] 客户端 ${socket.id} 订阅选股任务进度: ${taskId}`);
            socket.join(`task_${taskId}`);

            // 检查房间状态
            const room = stockProgressNamespace.adapter.rooms.get(`task_${taskId}`);
            const clientCount = room ? room.size : 0;
            logger.info(`[DEBUG] 房间 task_${taskId} 现在有 ${clientCount} 个客户端`);

            // 发送订阅确认
            socket.emit('subscribe_confirmed', { taskId, success: true });
            logger.info(`[DEBUG] 已发送订阅确认给客户端 ${socket.id}`);
        });

        // 监听客户端取消订阅
        socket.on('unsubscribe_task', (taskId) => {
            logger.info(`[DEBUG] 客户端 ${socket.id} 取消订阅选股任务进度: ${taskId}`);
            socket.leave(`task_${taskId}`);
            // 发送取消订阅确认
            socket.emit('unsubscribe_confirmed', { taskId, success: true });
        });

        // 断开连接
        socket.on('disconnect', () => {
            logger.info(`[DEBUG] 选股进度连接断开: ${socket.id}`);
            logger.info(`[DEBUG] 当前命名空间连接数: ${stockProgressNamespace.sockets.size}`);
        });
    });
    
    // 将命名空间引用传递给选股处理器
    const { symbolSelectHandler } = require('./symbolSelectHandler');
    if (typeof symbolSelectHandler.setSocketNamespace === 'function') {
        // 检查选股处理器是否已完全初始化
        if (symbolSelectHandler.isInitialized) {
            symbolSelectHandler.setSocketNamespace(stockProgressNamespace);
            logger.info(`选股处理器命名空间设置成功`);
        } else {
            logger.warn(`选股处理器尚未完全初始化，将在初始化完成后设置命名空间`);
            // 监听初始化完成事件，如果选股处理器支持事件机制
            symbolSelectHandler.on('initialized', () => {
                symbolSelectHandler.setSocketNamespace(stockProgressNamespace);
                logger.info(`选股处理器初始化后命名空间设置成功`);
            });
        }
    }
    
    logger.info(`选股进度Socket.IO服务初始化完成`);

    // 连接事件处理
    realtimeNamespace.on('connection', (socket) => {
        logger.debug(`实时数据命名空间收到新连接: ${socket.id}`);
        updateUserActivity(socket);
    });

    tradeNamespace.on('connection', (socket) => {
        logger.debug(`交易命名空间收到新连接: ${socket.id}`);
        updateUserActivity(socket);
    });
}

/**
 * 更新用户活动状态
 */
function updateUserActivity(socket) {
    // 从socket中获取用户信息（如果有的话）
    const username = socket.handshake.auth?.username || 'unknown';

    if (!userConnections.has(username)) {
        userConnections.set(username, {
            port: SOCKET_CONFIG.port, // 固定端口
            connections: new Set(),
            last_activity: Date.now()
        });
    }

    const userInfo = userConnections.get(username);
    userInfo.connections.add(socket.id);
    userInfo.last_activity = Date.now();

    // 监听断开连接
    socket.on('disconnect', () => {
        userInfo.connections.delete(socket.id);
        if (userInfo.connections.size === 0) {
            logger.debug(`用户 ${username} 所有连接已断开`);
        }
    });
}

/**
 * 获取可用的WebSocket端口 (保持API兼容性)
 * @param {string} username 用户名
 * @returns {number} 固定的端口号
 */
function getAvailablePort(username) {
    // 确保服务器已启动
    if (!isServerRunning) {
        startGlobalSocketServer();
    }

    // 记录用户连接
    if (!userConnections.has(username)) {
        userConnections.set(username, {
            port: SOCKET_CONFIG.port,
            connections: new Set(),
            assigned_at: Date.now(),
            last_activity: Date.now()
        });
    }

    logger.info(`为用户 ${username} 分配Socket.IO端口: ${SOCKET_CONFIG.port}`);
    return SOCKET_CONFIG.port;
}

/**
 * 释放WebSocket端口 (保持API兼容性)
 * @param {number} port 要释放的端口号
 * @param {string} username 用户名 (用于验证)
 * @returns {boolean} 是否成功释放
 */
function releasePort(port, username) {
    // 在单端口架构中，我们只需要清理用户连接记录
    if (userConnections.has(username)) {
        const userInfo = userConnections.get(username);

        // 断开该用户的所有连接
        if (globalIo) {
            userInfo.connections.forEach(socketId => {
                const socket = globalIo.sockets.sockets.get(socketId);
                if (socket) {
                    socket.disconnect(true);
                }
            });
        }

        // 清理用户连接记录
        userConnections.delete(username);
        logger.info(`用户 ${username} 的连接已清理`);
        return true;
    }

    logger.info(`用户 ${username} 没有活跃连接需要释放`);
    return true; // 返回true保持兼容性
}

/**
 * 停止全局Socket.IO服务器
 */
function stopGlobalSocketServer() {
    if (!isServerRunning) {
        logger.info('Socket.IO服务器未运行');
        return;
    }

    try {
        if (globalIo) {
            globalIo.close();
        }
        if (globalServer) {
            globalServer.close();
        }

        userConnections.clear();
        isServerRunning = false;
        logger.info('Socket.IO服务器已停止');
    } catch (error) {
        logger.error(`停止Socket.IO服务器失败: ${error.message}`);
    }
}

/**
 * 清理过期的用户连接
 * 定期检查长时间未活动的用户连接，并清理它们
 */
function cleanupInactiveConnections() {
    const now = Date.now();
    const inactivityThreshold = 30 * 60 * 1000; // 30分钟

    logger.debug('开始清理不活跃的用户连接...');

    userConnections.forEach((userInfo, username) => {
        if (userInfo.last_activity) {
            const inactiveTime = now - userInfo.last_activity;

            if (inactiveTime > inactivityThreshold) {
                logger.info(`用户 ${username} 已超过 ${inactivityThreshold / 60000} 分钟未活动，自动清理连接`);

                // 断开该用户的所有连接
                if (globalIo) {
                    userInfo.connections.forEach(socketId => {
                        const socket = globalIo.sockets.sockets.get(socketId);
                        if (socket) {
                            socket.disconnect(true);
                        }
                    });
                }

                // 清理用户连接记录
                userConnections.delete(username);
            }
        }
    });

    logger.debug('用户连接清理完成');
}

// 启动全局Socket.IO服务器
startGlobalSocketServer();

// 设置定期清理任务
setInterval(cleanupInactiveConnections, 10 * 60 * 1000); // 每10分钟清理一次

// API路由

// 获取可用的WebSocket端口
router.post('/get_port', authenticateToken, (req, res) => {
    try {
        const username = req.user?.username;

        if (!username) {
            return res.status(401).json({
                success: false,
                message: '未授权，请先登录'
            });
        }

        const port = getAvailablePort(username);

        if (port) {
            return res.json({
                success: true,
                port: port,
                message: '成功分配WebSocket端口'
            });
        } else {
            return res.status(503).json({
                success: false,
                message: '无可用的WebSocket端口'
            });
        }
    } catch (error) {
        logger.error(`获取WebSocket端口失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 释放WebSocket端口
router.post('/release_port', authenticateToken, (req, res) => {
    try {
        const { port } = req.body;
        const username = req.user?.username;

        if (!username) {
            return res.status(401).json({
                success: false,
                message: '未授权，请先登录'
            });
        }

        if (!port) {
            return res.status(400).json({
                success: false,
                message: '缺少端口参数'
            });
        }

        const success = releasePort(parseInt(port), username);

        if (success) {
            return res.json({
                success: true,
                message: '成功释放WebSocket端口'
            });
        } else {
            return res.status(400).json({
                success: false,
                message: '释放端口失败，可能端口不存在或不属于当前用户'
            });
        }
    } catch (error) {
        logger.error(`释放WebSocket端口失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 获取Socket服务器状态 (保持API兼容性)
router.get('/pool_status', authenticateToken, (req, res) => {
    try {
        // TODO: 添加管理员权限检查

        // 模拟端口池状态以保持API兼容性
        const status = [{
            port: SOCKET_CONFIG.port,
            status: isServerRunning ? 'in_use' : 'available',
            assigned_to: 'global_server',
            assigned_at: isServerRunning ? new Date().toISOString() : null,
            last_activity: isServerRunning ? new Date().toISOString() : null,
            error: null,
            user_connections: userConnections.size,
            total_connections: globalIo ? globalIo.sockets.sockets.size : 0
        }];

        return res.json({
            success: true,
            data: status
        });
    } catch (error) {
        logger.error(`获取Socket服务器状态失败: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// 导出模块
module.exports = {
    router,
    getAvailablePort,
    releasePort
};
# 量化交易系统模块说明

## 系统架构概述

本系统采用前后端分离架构，前端使用React+TypeScript，后端使用Node.js+Express，数据库使用MySQL。系统核心功能包括策略管理、回测、实盘交易等。

## 核心模块列表

### 1. 前端模块

#### 1.1 策略管理模块 (`frontend/src/_Pages/Dashboard`)
- **功能**: 策略列表展示、策略详情查看、策略编辑
- **主要组件**: 
  - `StrategyShower`: 策略卡片展示组件
  - `LiveStrategyPanel`: 实盘策略管理面板
- **日志标识**: `[策略管理]`

#### 1.2 实盘交易模块 (`frontend/src/_Widgets/LiveStrategyConfigModal`)
- **功能**: 实盘策略配置、部署、启停控制
- **主要组件**:
  - `LiveStrategyConfigModal`: 实盘策略配置对话框
- **新增功能**: 策略类型选择（strategyType字段）
- **日志标识**: `[实盘配置]`

#### 1.3 图表模块 (`frontend/src/_Modules/Chart`)
- **功能**: K线图表显示、技术指标、绘图工具
- **日志标识**: `[图表模块]`

#### 1.4 市场数据模块 (`frontend/src/_Modules/Market`)
- **功能**: 行情数据获取、实时数据订阅
- **日志标识**: `[市场数据]`

#### 1.5 信号计算模块 (`frontend/src/_Modules/Signal`)
- **功能**: 交易信号计算、信号显示
- **日志标识**: `[信号计算]`

#### 1.6 品种搜索模块 (`frontend/src/_Widgets/KeyboardCoder`)
- **功能**: 品种代码输入、实时搜索、品种选择
- **主要组件**: 
  - `KeyboardCoder`: 品种输入组件
  - `SymbolCacheService`: 品种缓存服务
- **新增功能**: 前端品种缓存、防抖搜索、智能排序
- **日志标识**: `[KeyboardCoder]`

#### 1.7 品种缓存服务 (`frontend/src/_Services/symbolCacheService.ts`)
- **功能**: 前端品种数据缓存、本地存储、搜索优化
- **核心特性**:
  - 24小时本地缓存
  - 内存+localStorage双重缓存
  - 智能搜索算法（代码/名称匹配）
  - 搜索结果排序优化
- **日志标识**: `[品种缓存]`

### 2. 后端模块

#### 2.1 策略处理器 (`backend/_Handlers/liveStrategyHandler.js`)
- **功能**: 实盘策略的CRUD操作、状态管理
- **新增功能**: 处理策略类型字段（strategyType）
- **日志标识**: `[LiveStrategyHandler]`

#### 2.2 实盘进程管理器 (`backend/services/LiveProcessManager.js`)
- **功能**: 管理Python实盘引擎进程、Redis通信
- **架构模式**: Manager-Worker模式
- **日志标识**: `[LiveProcessManager]`

#### 2.3 实盘策略适配器 (`backend/services/LiveStrategyAdapter.js`)
- **功能**: 适配前端API与底层实盘架构
- **新增功能**: 支持策略类型字段处理
- **日志标识**: `[适配器]`

#### 2.4 交易处理器 (`backend/_Handlers/tradeHandler`)
- **功能**: 交易通道管理、订单执行
- **日志标识**: `[交易处理]`

#### 2.5 市场数据处理器 (`backend/_Handlers/marketHandler.js`)
- **功能**: 品种数据管理、搜索服务、缓存管理
- **核心特性**:
  - 多市场品种数据获取（A股、美股、期货、加密货币）
  - 内存缓存机制
  - 拼音搜索支持
  - 智能匹配算法
- **日志标识**: `[MarketHandler]`

### 3. Python引擎模块

#### 3.1 实盘引擎 (`backend/_Providers/_Python/strategy/base/live_engine.py`)
- **功能**: 策略执行、数据处理、信号计算
- **架构**: 事件驱动、非阻塞处理
- **日志标识**: `[实盘引擎]`

#### 3.2 数据服务器 (`backend/_Providers/_Python/tdxserver.py`)
- **功能**: 行情数据获取、数据推送
- **日志标识**: `[数据服务器]`

#### 3.3 策略基类 (`backend/_Providers/_Python/strategy/base/MultiFactorsCTA_RT.py`)
- **功能**: 多因子CTA策略实现
- **日志标识**: `[策略基类]`

## 新增功能：策略类型管理

### 功能概述
为实盘策略系统添加了策略类型（strategyType）字段，支持不同类型策略的分类管理。

### 涉及的修改

#### 数据库层面
- 在 `live_strategies` 表中添加 `strategytype` 字段
- 默认值为 'portfolio'（投资组合策略）

#### 后端修改
1. **模型层** (`backend/models/LiveStrategy.js`)
   - 添加 `strategyType` 字段定义
   - 导入策略类型映射常量

2. **处理器层** (`backend/_Handlers/liveStrategyHandler.js`)
   - `handleDeployToLive`: 处理新部署时的策略类型
   - `handleUpdateLiveStrategy`: 处理策略配置更新时的类型修改

3. **服务层** (`backend/services/LiveStrategyAdapter.js`)
   - `deployStrategy`: 创建实盘策略时保存策略类型
   - `getLiveStrategies`: 返回策略列表时包含策略类型

#### 前端修改
1. **类型定义**
   - `frontend/src/shared_types/strategy.ts`: 添加策略类型映射和选项
   - `frontend/src/shared_types/trade.ts`: LiveStrategyInfo接口添加strategyType字段
   - `frontend/src/events/events.ts`: 相关事件接口添加策略类型字段

2. **组件修改**
   - `LiveStrategyConfigModal`: 添加策略类型选择下拉框
   - `LiveStrategyPanel`: 传递当前策略类型到配置对话框

### 策略类型定义
```typescript
export const STRATEGY_TYPE_MAP = {
  PORTFOLIO: 'portfolio',
  // 未来可扩展更多类型
} as const;

export const STRATEGY_TYPE_OPTIONS = [
  { value: STRATEGY_TYPE_MAP.PORTFOLIO, label: '投资组合策略' },
  // 未来可扩展更多类型
];
```

## 系统通信架构

### 前端通信
- **事件总线**: 使用mitt库实现模块间解耦通信
- **全局状态**: 使用Jotai管理共享状态
- **HTTP通信**: 与后端API交互

### 后端通信
- **Redis**: 进程间通信、数据缓存
- **WebSocket**: 实时数据推送
- **HTTP API**: RESTful接口服务

### 实盘架构
- **管理者-执行者模式**: Node.js管理器 + Python执行器
- **数据流**: tdxserver → Redis → live_engine → 策略计算
- **控制流**: 前端 → Node.js → Redis控制频道 → Python引擎

## 开发规范

1. **日志规范**: 所有模块日志必须包含模块标识，格式：`console.log([模块名]消息内容)`
2. **代码分层**: 严格按照Controller → Service → Model的分层架构
3. **事件驱动**: 前端模块间通过事件总线通信，避免直接依赖
4. **类型安全**: 所有接口和数据结构必须有明确的TypeScript类型定义
5. **错误处理**: 完善的错误捕获和用户友好的错误提示

## 部署说明

1. **前端**: React应用，构建后部署到Web服务器
2. **后端**: Node.js服务，需要配置数据库连接和Redis连接
3. **Python引擎**: 独立进程，由Node.js管理器启动和监控
4. **数据库**: MySQL，需要运行迁移脚本创建表结构

## 后端模块

### TDX 数据服务 (`backend/_Providers/_Python/tdxserver.py`)

*   **类型**: Python Flask 本地服务器
*   **功能**: 通过 `pytdx` 库连接通达信行情服务器，提供实时数据接口。
*   **运行方式**: 在 `backend/_Providers/_Python/` 目录下运行 `python tdxserver.py`。
*   **接口**:
    *   `/kline` (GET): 获取指定品种、周期、数量的 K 线数据。
    *   `/quote` (GET): 获取指定品种的实时五档行情数据。
*   **日志标识**: `[TDX 数据服务]`

---

*(后续其他模块将在此处添加)* 
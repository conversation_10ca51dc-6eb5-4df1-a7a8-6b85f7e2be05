{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import jqktrader\n", "\n", "user = jqktrader.use()\n", "\n", "user.connect(\n", "  exe_path=r'D:\\同花顺软件\\同花顺\\xiadan.exe',\n", "  tesseract_cmd=r'D:\\Program Files\\Tesseract-OCR\\tesseract.exe'\n", ")\n", "\n", "user.position"]}], "metadata": {"kernelspec": {"display_name": "Python 3.10.4 ('jqktrader-EOpNXzYo-py3.10')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "427174d10de08e7ba0b097af1f04ac0363ae87e81f723d609b62824178bcb47e"}}}, "nbformat": 4, "nbformat_minor": 2}
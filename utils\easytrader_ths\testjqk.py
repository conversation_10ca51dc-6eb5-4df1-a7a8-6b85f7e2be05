import sys
sys.path.insert(0, r'D:/projects/quantquart2/jqktrader')
import jqktrader

# 初始化同花顺客户端
user = jqktrader.use()
user.connect(
    exe_path=r'D:\同花顺软件\同花顺\xiadan.exe',
    tesseract_cmd=r'D:\tools\Tesseract-OCR\tesseract.exe'
)

def print_menu():
    print("\n=== jqktrader 同花顺测试菜单 ===")
    print("1. 查询账户余额")
    print("2. 查询持仓")
    print("3. 买入股票")
    print("4. 卖出股票")
    print("5. 查询今日委托")
    print("6. 查询今日成交")
    print("7. 刷新数据")
    print("0. 退出")
    print("==============================")

def main():
    while True:
        print_menu()
        choice = input("请输入操作编号: ").strip()
        if choice == '1':
            try:
                print("[余额]", user.balance)
            except Exception as e:
                print("[余额] 查询失败:", e)
        elif choice == '2':
            try:
                print("[持仓]", user.position)
            except Exception as e:
                print("[持仓] 查询失败:", e)
        elif choice == '3':
            code = input("股票代码: ").strip()
            price = input("买入价格: ").strip()
            amount = input("买入数量: ").strip()
            try:
                result = user.buy(code, price=float(price), amount=int(amount))
                print("[买入]", result)
            except Exception as e:
                print("[买入] 操作失败:", e)
        elif choice == '4':
            code = input("股票代码: ").strip()
            price = input("卖出价格: ").strip()
            amount = input("卖出数量: ").strip()
            try:
                result = user.sell(code, price=float(price), amount=int(amount))
                print("[卖出]", result)
            except Exception as e:
                print("[卖出] 操作失败:", e)
        elif choice == '5':
            try:
                print("[今日委托]", user.today_entrusts)
            except Exception as e:
                print("[今日委托] 查询失败:", e)
        elif choice == '6':
            try:
                print("[今日成交]", user.today_trades)
            except Exception as e:
                print("[今日成交] 查询失败:", e)
        elif choice == '7':
            try:
                user.refresh()
                print("[刷新] 已请求刷新")
            except Exception as e:
                print("[刷新] 操作失败:", e)
        elif choice == '0':
            print("退出程序。"); break
        else:
            print("无效输入，请重新选择。")

if __name__ == "__main__":
    main()
import React, { useState, useEffect, useRef } from 'react';
import { AutoComplete } from 'antd';
import { useAtom } from 'jotai';
import { selectedSymbolAtom } from '@/store/state';
import { Symbol, MarketType } from '@/shared_types/market';
import { marketService } from '@/_Services/marketService';
import { symbolCacheService } from '@/_Services/symbolCacheService';
import { EventBus } from '@/events/eventBus';
import { MarketEvents, KeyboardEvents } from '@/events/events';
import { useTheme } from '@/models/useTheme';
import './index.less';

// --- 新增：定义 Props 接口 ---
interface KeyboardCoderProps {
  /**
   * 是否以内联模式显示组件
   * @default false
   */
  inline?: boolean;
}
// --- 新增结束 ---

/**
 * 键盘代码输入组件
 * 用于输入股票代码并选择股票
 */
// --- 修改：接受 props 并解构 inline ---
const KeyboardCoder: React.FC<KeyboardCoderProps> = ({ inline = false }) => {
  // 搜索结果状态
  const [options, setOptions] = useState<{ value: string; label: string; market: MarketType; exchange: string }[]>([]);
  const [searchValue, setSearchValue] = useState('');
  // 控制组件可见性
  const [visible, setVisible] = useState(true);
  // 防抖定时器
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 获取主题状态
  const { isDarkMode } = useTheme();

  // 全局选中的股票状态
  const [selectedSymbol, setSelectedSymbol] = useAtom(selectedSymbolAtom);

  // 引用AutoComplete组件
  const autoCompleteRef = useRef<any>(null);

  // 根据代码判断市场类型
  const getMarketType = (code: string): MarketType => {
    if (code.includes('.SH') || code.includes('.SZ')) {
      return MarketType.INDEX;
    }
    // 可以添加更多的判断逻辑
    return MarketType.STOCK;
  };

  // 处理搜索（带防抖）
  const handleSearch = async (value: string) => {
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (!value) {
      console.log('[KeyboardCoder] 搜索值为空，清空选项');
      setOptions([]);
      return;
    }

    // 设置防抖延迟（300ms）
    searchTimeoutRef.current = setTimeout(async () => {
      try {
        // 使用前端缓存服务进行搜索
        const symbols = symbolCacheService.searchSymbols(value);
        console.log(`[KeyboardCoder] 前端搜索完成，找到 ${symbols.length} 个匹配项`);
        
        setOptions(symbols.map(symbol => ({
          value: symbol.code,
          label: `${symbol.code} - ${symbol.name}`,
          market: symbol.market,
          exchange: symbol.exchange
        })));
      } catch (error) {
        console.error('[KeyboardCoder] 前端搜索失败，回退到后端搜索:', error);
        
        // 如果前端搜索失败，回退到后端搜索
        try {
          const symbols = await marketService.getSymbols(value);
          setOptions(symbols.map(symbol => ({
            value: symbol.code,
            label: `${symbol.code} - ${symbol.name}`,
            market: symbol.market,
            exchange: symbol.exchange
          })));
        } catch (backendError) {
          console.error('[KeyboardCoder] 后端搜索也失败:', backendError);
          setOptions([]);
        }
      }
    }, 300);
  };

  // 订阅搜索结果事件
  useEffect(() => {
    const unsubscribe = EventBus.on(MarketEvents.Types.SEARCH_RESULT, (payload: MarketEvents.SearchResult) => {
      console.log('[KeyboardCoder] 收到搜索结果事件:', payload);
      // 将搜索结果转换为AutoComplete选项格式
      setOptions(payload.data.map(symbol => ({
        value: symbol.code,
        label: `${symbol.code} - ${symbol.name}`,
        market: symbol.market,
        exchange: symbol.exchange
      })));
    });

    return () => unsubscribe.unsubscribe();
  }, []);

  // 初始化缓存检查
  useEffect(() => {
    const checkCacheStatus = async () => {
      const cacheStatus = symbolCacheService.getCacheStatus();
      console.log('[KeyboardCoder] 初始化时缓存状态:', cacheStatus);
      
      if (!cacheStatus.hasData) {
        console.log('[KeyboardCoder] 缓存为空，开始加载品种数据');
        try {
          await symbolCacheService.loadAllSymbols();
          console.log('[KeyboardCoder] 品种数据加载完成');
        } catch (error) {
          console.error('[KeyboardCoder] 品种数据加载失败:', error);
        }
      }
    };

    checkCacheStatus();
  }, []);

  // 监听键盘事件
  useEffect(() => {
    // 订阅键盘按键事件
    const keyPressSubscription = EventBus.on(
      KeyboardEvents.Types.KEY_PRESSED,
      (payload: KeyboardEvents.KeyPressed) => {
        console.log('[KeyboardCoder] 收到键盘按键事件:', payload);
      }
    );

    // 订阅开始输入事件
    const inputStartSubscription = EventBus.on(
      KeyboardEvents.Types.INPUT_STARTED,
      (payload: KeyboardEvents.InputStarted) => {
        console.log('[KeyboardCoder] 收到开始输入事件:', payload);
        // 先清除当前焦点，防止其他元素抢夺焦点
        if (document.activeElement && document.activeElement !== document.body) {
          (document.activeElement as HTMLElement).blur();
        }
        
        // 显示组件
        setVisible(true);
        // 设置初始字符
        setSearchValue(payload.initialKey);

        // 组件出现后自动聚焦
        setTimeout(() => {
          if (autoCompleteRef.current) {
            const input = autoCompleteRef.current.input;
            if (input) {
              console.log('[KeyboardCoder] 尝试设置焦点到输入框');
              
              // 强制清除所有焦点
              if (document.activeElement && document.activeElement !== document.body) {
                (document.activeElement as HTMLElement).blur();
              }
              
              // 使用 requestAnimationFrame 确保在下一帧设置焦点
              requestAnimationFrame(() => {
                input.focus();
                console.log('[KeyboardCoder] 焦点设置完成，当前焦点元素:', document.activeElement);
              });
            } else {
              console.log('[KeyboardCoder] 未找到输入框元素');
            }
          } else {
            console.log('[KeyboardCoder] 未找到AutoComplete引用');
          }
        }, 200);

        // 触发搜索
        handleSearch(payload.initialKey);
        
        // 检查缓存状态并输出日志
        const cacheStatus = symbolCacheService.getCacheStatus();
        console.log('[KeyboardCoder] 缓存状态:', cacheStatus);
      }
    );

    // 订阅取消输入事件
    const inputCancelledSubscription = EventBus.on(
      KeyboardEvents.Types.INPUT_CANCELLED,
      (payload: KeyboardEvents.InputCancelled) => {
        console.log('[KeyboardCoder] 收到取消输入事件:', payload);
        setSearchValue('');
        setVisible(false);
      }
    );

    // 订阅品种按钮点击事件
    const symbolButtonClickSubscription = EventBus.on(
      KeyboardEvents.Types.SYMBOL_BUTTON_CLICKED,
      () => {
        console.log('[KeyboardCoder] 收到品种按钮点击事件，显示输入框');
        
        // 先清除当前焦点，防止其他元素抢夺焦点
        if (document.activeElement && document.activeElement !== document.body) {
          (document.activeElement as HTMLElement).blur();
        }
        
        setVisible(true);
        setSearchValue('');
        
        // 组件出现后自动聚焦
        setTimeout(() => {
          if (autoCompleteRef.current) {
            const input = autoCompleteRef.current.input;
            if (input) {
              input.focus();
            }
          }
        }, 100);
      }
    );

    // 清理订阅
    return () => {
      keyPressSubscription.unsubscribe();
      inputStartSubscription.unsubscribe();
      inputCancelledSubscription.unsubscribe();
      symbolButtonClickSubscription.unsubscribe();
      
      // 清理防抖定时器
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // 用于防止重复选择的标记
  const lastSelectionRef = useRef<{ value: string; timestamp: number } | null>(null);

  // 选择股票
  const handleSelect = (value: string) => {
    // 防止重复选择：检查是否在短时间内（200ms）选择了相同的值
    const now = Date.now();
    if (lastSelectionRef.current &&
        lastSelectionRef.current.value === value &&
        now - lastSelectionRef.current.timestamp < 200) {
      console.log(`[KeyboardCoder] 忽略重复选择: ${value}`);
      return;
    }

    // 更新最后选择的记录
    lastSelectionRef.current = { value, timestamp: now };

    // 设置搜索值
    setSearchValue(value);
    const symbol = options.find(opt => opt.value === value);
    if (symbol) {
      setSelectedSymbol({
        code: value,
        name: symbol.label.split(' - ')[1],
        market: symbol.market || getMarketType(value),
        exchange: symbol.exchange
      } as Symbol);

      // 通知系统股票已变更
      EventBus.emit(MarketEvents.Types.SYMBOL_CHANGED, {
        symbol: {
          code: value,
          name: symbol.label.split(' - ')[1],
          market: symbol.market || getMarketType(value),
          exchange: symbol.exchange
        } as Symbol
      });

      // 提取基础代码 (移除 .SZ/.SH 等)
      const baseCode = symbol.value.split('.')[0] || symbol.value;
      // 发送包含 code, market, exchange 的事件
      EventBus.emit(KeyboardEvents.Types.INPUT_COMPLETE, {
        code: baseCode,
        market: symbol.market, // MarketType enum value (string)
        exchange: symbol.exchange, // ExchangeType enum value (string)
        timestamp: now
      });

      (document.activeElement as HTMLElement).blur();
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
    // 只处理 Escape 键，Enter 键由 AutoComplete 的 onSelect 处理
    if (e.key === 'Escape') {
      setVisible(false);
      EventBus.emit(KeyboardEvents.Types.INPUT_CANCELLED, {
        reason: 'escape_pressed',
        timestamp: Date.now()
      });
    }

    // 如果按下 Enter 键但没有选中任何选项（即没有触发 onSelect）
    // 这种情况下，我们需要手动处理
    if (e.key === 'Enter' && options.length === 0) {
      // 如果没有匹配的选项，可以选择不做任何操作
      console.log('[KeyboardCoder] 按下 Enter 键但没有匹配的选项');
    }
  };

  if (!visible) {
    return null;
  }

  // --- 修改：根据 inline 模式选择根元素的类名 ---
  const rootClassName = inline
    ? `keyboard-coder-inline ${isDarkMode ? 'dark' : 'light'}`
    : `keyboard-coder ${isDarkMode ? 'dark' : 'light'}`;
  // --- 修改结束 ---

  return (
    // --- 修改：使用动态类名 ---
    <div className={rootClassName}>
      <AutoComplete
        ref={autoCompleteRef}
        placeholder="输入股票代码"
        value={searchValue}
        options={options}
        onChange={setSearchValue}
        onSearch={handleSearch}
        onSelect={handleSelect}
        onKeyDown={handleKeyDown}
        // --- 修改：根据 inline 模式调整宽度和大小 ---
        style={{
          width: inline ? '150px' : '100%',
          minWidth: inline ? '150px' : 'auto'
        }}
        allowClear={true}
        size={inline ? "middle" : "large"} // 内联模式使用中等大小
        autoFocus
        // 设置下拉菜单的样式
        dropdownStyle={{ minWidth: inline ? '200px' : 'auto' }}
        dropdownMatchSelectWidth={false} // 允许下拉菜单宽度不受输入框限制
      />
    </div>
    // --- 修改结束 ---
  );
};

export default KeyboardCoder;
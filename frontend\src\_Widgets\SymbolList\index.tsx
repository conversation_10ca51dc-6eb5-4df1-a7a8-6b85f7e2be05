import React, { useState, useEffect, useCallback } from 'react';
import { Table, Typography, Space, Button, Tooltip, Checkbox } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useAtom } from 'jotai';
import { selectedSymbol<PERSON>tom, selectedPeriod<PERSON>tom, symbolList<PERSON>ame<PERSON>tom, symbolListIsSystemAtom } from '@/store/state';
import { EventBus } from '@/events/eventBus';
import { MarketEvents, ChartEvents } from '@/events/events';
import { Symbol, KLineInterval, ExchangeType, MarketType } from '@/shared_types/market'; // Import Symbol type, KLineInterval, and specific types
import poolDispatcher from '@/_Dispatchers/poolDispatcher'; // 导入 poolDispatcher
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { MdOutlineStackedLineChart } from 'react-icons/md'; // 导入多品种叠加图标
import './index.less';

// Helper function to parse the symbol string back into a Symbol object
const parseSymbolString = (symbolString: string): Omit<Symbol, 'name'> | null => {
  if (!symbolString || typeof symbolString !== 'string') return null;
  const parts = symbolString.split('.');
  if (parts.length >= 3) {
    // Cast parts to specific types
    const exchange = parts[0] as ExchangeType;
    const market = parts[1] as MarketType;
    const code = parts.slice(2).join('.');
    return {
      exchange: exchange,
      market: market,
      code: code,
    };
  }
  return null;
};

/**
 * 从完整的 symbol 字符串中提取代码部分。
 * @param symbolString - 完整的 symbol 字符串，例如 "SSE.STOCK.600010"。
 * @returns 代码部分，例如 "600010"，如果解析失败则返回 null。
 */
const extractCodeFromSymbolString = (symbolString: string): string | null => {
  if (!symbolString || typeof symbolString !== 'string') return null;
  const parts = symbolString.split('.');
  // 确保至少有三个部分（交易所.市场.代码）
  if (parts.length >= 3) {
    // 代码部分是从第三个元素开始的所有部分连接起来
    return parts.slice(2).join('.');
  }
  return null; // 如果格式不正确，返回 null
};

const SymbolList: React.FC = () => {
  // Use the updated SymbolListItem type from events.ts
  const [symbolList, setSymbolList] = useState<MarketEvents.SymbolListItem[]>([]);
  const [, setSelectedSymbol] = useAtom(selectedSymbolAtom);
  const [, setSelectedPeriod] = useAtom(selectedPeriodAtom);

  // 当前的列表名称和是否系统列表，使用 useState 管理
  const [currentListName, setCurrentListName] = useAtom(symbolListNameAtom); // 改为全局atom
  const [isSystemList, setIsSystemList] = useAtom(symbolListIsSystemAtom); // 改为全局atom

  // 比较模式状态
  const [compareMode, setCompareMode] = useState(false);
  const [selectedSymbols, setSelectedSymbols] = useState<string[]>([]);

  // Columns definition using the correct fields from the new structure
  const symbolColumns: ColumnsType<MarketEvents.SymbolListItem> = [
    ...(compareMode ? [
      {
        title: '',
        key: 'selection',
        width: 40,
        render: (_: any, record: MarketEvents.SymbolListItem) => (
          <Checkbox
            checked={selectedSymbols.includes(record.symbol)}
            onChange={(e) => {
              if (e.target.checked) {
                setSelectedSymbols([...selectedSymbols, record.symbol]);
              } else {
                setSelectedSymbols(selectedSymbols.filter(s => s !== record.symbol));
              }
            }}
            onClick={(e) => e.stopPropagation()} // 防止点击选择框时触发行点击事件
          />
        )
      }
    ] : []),
    {
      title: '交易对',
      key: 'symbol',
      render: (_: any, record: MarketEvents.SymbolListItem) => (
        <Space>
          {/* Use record.name for display name and increase font size */}
          <Typography.Text strong style={{ fontSize: '16px' }}>{record.name}</Typography.Text>
          {/* Use helper function to display only the code part */}
          <Typography.Text type="secondary">({extractCodeFromSymbolString(record.symbol) || record.symbol})</Typography.Text>
        </Space>
      )
    }
  ];

  // Click handler using the helper function
  const handleSymbolListItemClick = useCallback((item: MarketEvents.SymbolListItem) => {
    // 如果处于比较模式，点击行时切换选中状态
    if (compareMode) {
      const isSelected = selectedSymbols.includes(item.symbol);
      if (isSelected) {
        setSelectedSymbols(selectedSymbols.filter(s => s !== item.symbol));
      } else {
        setSelectedSymbols([...selectedSymbols, item.symbol]);
      }
      return;
    }

    console.log("[品种列表] 点击2: ", item);

    // Parse the symbol string back into an object
    const parsedSymbol = parseSymbolString(item.symbol);

    console.log("[品种列表] 解析后的 symbol:", parsedSymbol);

    if (parsedSymbol) {
      // Assign the top-level name to the symbol object before setting state
      const symbolObject: Symbol = {
        ...parsedSymbol,
        name: item.name,
      };
      console.log("[品种列表] 解析并重构 Symbol 对象:", symbolObject); // Debug log
      setSelectedSymbol(symbolObject);
      console.log("[品种列表] setSelectedSymbol 已调用"); // Debug log

      // 通知其他组件历史记录被点击，关闭历史面板，添加移动端判断
      EventBus.emit(ChartEvents.Types.TOGGLE_SYMBOLLIST, {
        isMobile: window.innerWidth <= 768,
        visible: false
      });
    } else {
      console.error("[品种列表] 无法解析点击的 symbol 字符串:", item.symbol);
    }
  }, [compareMode, selectedSymbols, setSelectedSymbols, setSelectedSymbol]); // 添加比较模式相关依赖

  useEffect(() => {
    // 直接使用 poolDispatcher.getSymbolList 获取数据
    const fetchData = async () => {
      console.log(`[品种列表] 初始化或名称变更，直接获取列表数据: ${currentListName}`);
      try {
        // 调用 poolDispatcher.getSymbolList 获取数据
        const response = await poolDispatcher.getSymbolList(currentListName, isSystemList);

        if (response && response.data) {
          console.log(`[品种列表] 成功获取列表数据，名称: ${response.listName}, 数据长度: ${response.data.length}`);
          if (response.data.length > 0) {
            console.log(`[品种列表] 第一项示例:`, response.data[0]);
          }

          const listData = response.data;

          if (Array.isArray(listData)) {
            // Filter out invalid items before setting state
            const validListData = listData.filter(item =>
                item &&
                typeof item.symbol === 'string' && item.symbol.includes('.') &&
                typeof item.name === 'string' && item.name.trim() !== ''
                // 不再要求 item.data 属性
            );

            console.log(`[品种列表] 验证前数据长度: ${listData.length}, 验证后数据长度: ${validListData.length}`);
            if (listData.length > 0 && validListData.length === 0) {
              console.log(`[品种列表] 警告: 所有数据项都未通过验证，检查第一项:`, listData[0]);
            }

            setSymbolList(validListData as MarketEvents.SymbolListItem[]);
          } else {
            console.error("[品种列表] 获取的数据不是数组:", listData);
            setSymbolList([]);
          }
        } else {
          console.error("[品种列表] 获取列表数据失败:", response);
          setSymbolList([]);
        }
      } catch (e) {
        console.error("[品种列表] 获取列表数据时发生错误:", e);
        setSymbolList([]);
      }
    };

    fetchData();

    // 监听列表变化事件，以便其他组件可以通知 SymbolList 更新
    const unsubscribeChanged = EventBus.on(MarketEvents.Types.SYMBOLLIST_CHANGED, (payload: MarketEvents.SymbolListChanged) => {
      console.log(`[品种列表] 收到列表变化事件，名称: ${payload.symbolListName}, 是否系统: ${payload.isSystemList}`);

      // 仅在属于自己的事件进行响应
      if (payload.tag === 1) {
        setCurrentListName(payload.symbolListName);
        setIsSystemList(payload.isSystemList); // 更新 isSystemList 状态
      }
      // 不需要在这里重新获取数据，因为 currentListName 和 isSystemList 变化会触发 useEffect
    });

    // 监听列表更新事件，当列表内容更新时刷新数据
    const unsubscribeUpdated = EventBus.on(MarketEvents.Types.SYMBOLLIST_UPDATED, (payload: MarketEvents.SymbolListUpdatedPayload) => {
      console.log(`[品种列表] 收到列表更新事件，名称: ${payload.listName}, 是否系统: ${payload.isSystemList}`);

      // 如果更新的是当前正在显示的列表，则更新数据
      if (payload.listName === currentListName && payload.isSystemList === isSystemList) {
        console.log(`[品种列表] 更新的是当前列表，直接更新数据`);

        if (payload.theList && Array.isArray(payload.theList)) {
          // 过滤无效数据
          const validListData = payload.theList.filter(item =>
            item &&
            typeof item.symbol === 'string' && item.symbol.includes('.') &&
            typeof item.name === 'string' && item.name.trim() !== ''
          );

          console.log(`[品种列表] 收到更新数据，长度: ${validListData.length}`);
          setSymbolList(validListData as MarketEvents.SymbolListItem[]);
        }
      }
    });

    return () => {
      unsubscribeChanged.unsubscribe();
      unsubscribeUpdated.unsubscribe();
    };
  }, [currentListName, isSystemList]);

  // 处理比较按钮点击
  const handleCompareClick = () => {
    setCompareMode(!compareMode);
    setSelectedSymbols([]);
  };

  // 处理确认比较
  const handleConfirmCompare = () => {
    if (selectedSymbols.length === 0) {
      // 如果没有选择任何品种，直接退出比较模式
      setCompareMode(false);
      return;
    }

    console.log("[品种列表] 确认比较，选中的品种:", selectedSymbols);

    // 构建品种代码串，用于多品种重叠指标
    const symbolsString = selectedSymbols.join(',');

    console.log("[品种列表] 发送创建多品种重叠指标事件，参数:", symbolsString);

    // 创建多品种重叠指标（只发送一次事件，包含所有选中的品种）
    EventBus.emit(ChartEvents.Types.CREATE_MULTI_SYMBOL_OVERLAY as any, {
      symbols: symbolsString
    });

    // 延迟关闭面板，确保事件先被处理
    setTimeout(() => {
      // 退出比较模式
      setCompareMode(false);
      setSelectedSymbols([]);

      // 关闭品种列表面板
      EventBus.emit(ChartEvents.Types.TOGGLE_SYMBOLLIST, {
        isMobile: window.innerWidth <= 768,
        visible: false
      });
    }, 100);
  };

  // 处理取消比较
  const handleCancelCompare = () => {
    setCompareMode(false);
    setSelectedSymbols([]);
  };

  return (
    <div className="symbol-list-container">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '10px' }}>
        <Button
          color="primary"
          variant="filled"
          style={{
            fontSize: '16px',
            fontWeight: 'bold',
            width: '100px'
          }}
          onClick={() => {
            if (!compareMode) {
              console.log("[品种列表] 点击列表名称按钮，打开股票池管理面板");
              EventBus.emit(MarketEvents.Types.SHOW_STOCK_POOL_MANAGER, {
                visible: true,
                currentListName: currentListName,
                tag: 1
              });
            }
          }}
        >
          {currentListName}
        </Button>

        {compareMode ? (
          <Space>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleConfirmCompare}
              size="small"
            />
            <Button
              danger
              icon={<CloseOutlined />}
              onClick={handleCancelCompare}
              size="small"
            />
          </Space>
        ) : (
          <Tooltip title="多品种叠加">
            <Button
              icon={<MdOutlineStackedLineChart style={{ fontSize: '16px' }} />}
              onClick={handleCompareClick}
              size="small"
            />
          </Tooltip>
        )}
      </div>

      <div className="list-content">
        <Table
          columns={symbolColumns}
          dataSource={symbolList}
          rowKey={(record: MarketEvents.SymbolListItem) => `${record.symbol}_${record.data?.viewedAt ?? record.name ?? ''}`}
          pagination={false}
          size="small"
          onRow={(record: MarketEvents.SymbolListItem) => {
            return {
              onClick: () => handleSymbolListItemClick(record),
              className: 'symbollist-table-row'
            };
          }}
          className="symbollist-table"
        />
      </div>
    </div>
  );
};

export default SymbolList;
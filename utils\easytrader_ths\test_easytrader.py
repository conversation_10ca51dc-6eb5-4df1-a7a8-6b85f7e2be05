import easytrader
import getpass

def print_menu():
    print("\n=== EasyTrader 同花顺测试菜单 ===")
    print("1. 查询账户余额")
    print("2. 查询持仓")
    print("3. 买入股票")
    print("4. 卖出股票")
    print("5. 查询今日委托")
    print("6. 查询今日成交")
    print("7. 刷新数据")
    print("0. 退出")
    print("===============================")

def main():
    print("=== EasyTrader 同花顺 测试工具 ===")
    ths_path = input("请输入同花顺客户端路径（如 C:\\同花顺\\xiadan.exe ）: ").strip()
    trader = easytrader.use('ths')
    trader.connect(ths_path)
    print("已连接同花顺客户端。")

    while True:
        print_menu()
        choice = input("请选择操作: ").strip()
        if choice == '1':
            print("账户余额：")
            print(trader.balance)
        elif choice == '2':
            print("持仓信息：")
            print(trader.position)
        elif choice == '3':
            code = input("股票代码: ").strip()
            price = float(input("买入价格: ").strip())
            amount = int(input("买入数量: ").strip())
            result = trader.buy(code, price=price, amount=amount)
            print("买入结果：", result)
        elif choice == '4':
            code = input("股票代码: ").strip()
            price = float(input("卖出价格: ").strip())
            amount = int(input("卖出数量: ").strip())
            result = trader.sell(code, price=price, amount=amount)
            print("卖出结果：", result)
        elif choice == '5':
            print("今日委托：")
            print(trader.today_entrusts)
        elif choice == '6':
            print("今日成交：")
            print(trader.today_trades)
        elif choice == '7':
            trader.refresh()
            print("已刷新数据。")
        elif choice == '0':
            print("退出程序。")
            break
        else:
            print("无效选择，请重新输入。")

if __name__ == "__main__":
    main()
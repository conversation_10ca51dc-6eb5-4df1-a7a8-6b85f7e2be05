// frontend/src/_Dispatchers/SymbolSelectDispatcher.ts
import { EventBus } from '../events/eventBus';
import { SymbolSelectEvents, MarketEvents } from '../events/events';
import axios from 'axios';
import { getToken } from '@/utils/auth';
import { poolCache, PoolItem } from '@/utils/PoolCache';
import { ShapeSignal } from '@/_Modules/Signal/signals/ShapeSignal';
import { io, Socket } from 'socket.io-client';

/**
 * 选股专用分发器
 * 负责处理前端选股事件、后端调用和进度反馈
 */
class SymbolSelectDispatcher {
  private isInitialized = false;
  private socket: Socket | null = null;
  private isConnected = false;
  private currentTaskId: string | null = null;

  /**
   * 初始化选股分发器
   */
  initialize() {
    if (this.isInitialized) {
      console.log('[选股分发器] 已经初始化过');
      return;
    }

    console.log('[选股分发器] 开始初始化');
    
    // 订阅选股相关事件
    this.subscribeToStockSelection();
    this.subscribeToProgressUpdates();
    
    // 初始化Socket.IO连接
    this.initSocketConnection();
    
    this.isInitialized = true;
    console.log('[选股分发器] 初始化完成');
  }

  /**
   * 初始化Socket.IO连接
   */
  private initSocketConnection() {
    console.log('[选股分发器] 初始化Socket.IO连接');
    
    // 创建Socket.IO连接
    const host = window.location.hostname;
    const port = 18888; // 使用固定端口
    
    this.socket = io(`http://${host}:${port}/stock_progress`, {
      path: '/socket.io',
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
      transports: ['polling', 'websocket']
    });
    
    // 连接事件
    this.socket.on('connect', () => {
      console.log('[选股分发器] Socket.IO连接成功');
      this.isConnected = true;
      
      // 如果有当前任务ID，立即订阅
      if (this.currentTaskId) {
        console.log('[选股分发器] Socket.IO连接成功，立即订阅当前任务:', this.currentTaskId);
        this.subscribeToTask(this.currentTaskId);
      } else {
        console.log('[选股分发器] Socket.IO连接成功，但无当前任务ID');
      }
    });
    
    // 连接错误事件
    this.socket.on('connect_error', (error) => {
      console.error('[选股分发器] Socket.IO连接错误:', error);
      this.isConnected = false;
    });
    
    // 断开连接事件
    this.socket.on('disconnect', () => {
      console.log('[选股分发器] Socket.IO连接断开');
      this.isConnected = false;
    });
    
    // 进度更新事件
    this.socket.on('progress_update', (progressData: SymbolSelectEvents.StockSelectionProgress) => {
      console.log('[选股分发器] [DEBUG] 收到Socket.IO进度更新:', progressData);
      console.log('[选股分发器] [DEBUG] 进度数据详情:', {
        taskId: progressData.taskId,
        stage: progressData.stage,
        current: progressData.current,
        total: progressData.total,
        selectedCount: progressData.selectedCount,
        progress: progressData.progress
      });

      // 转发进度事件到前端组件
      EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_PROGRESS, progressData);

      // 添加调试日志
      console.log('[选股分发器] [DEBUG] 已转发进度事件到前端组件，事件类型:', SymbolSelectEvents.Types.STOCK_SELECTION_PROGRESS);
    });
    
    // 订阅确认事件
    this.socket.on('subscribe_confirmed', (data) => {
      console.log('[选股分发器] 订阅确认:', data);
    });
    
    // 取消订阅确认事件
    this.socket.on('unsubscribe_confirmed', (data) => {
      console.log('[选股分发器] 取消订阅确认:', data);
    });
    
    // 错误事件
    this.socket.on('error', (error: any) => {
      console.error('[选股分发器] Socket.IO错误:', error);
    });
  }
  
  /**
   * 订阅任务进度
   */
  private subscribeToTask(taskId: string) {
    if (!this.isConnected || !this.socket) {
      console.warn('[选股分发器] 无法订阅任务进度，Socket.IO未连接');
      return;
    }
    
    console.log(`[选股分发器] 订阅任务进度: ${taskId}`);
    this.socket.emit('subscribe_task', taskId);
    this.currentTaskId = taskId;
    
    // 添加调试日志
    console.log(`[选股分发器] 已发送订阅请求，等待确认...`);
  }
  
  /**
   * 取消订阅任务进度
   */
  private unsubscribeFromTask(taskId: string) {
    if (!this.isConnected || !this.socket) {
      return;
    }
    
    console.log(`[选股分发器] 取消订阅任务进度: ${taskId}`);
    this.socket.emit('unsubscribe_task', taskId);
    
    if (this.currentTaskId === taskId) {
      this.currentTaskId = null;
    }
  }

  /**
   * 测试事件定义是否正确
   */
  testEventDefinitions() {
    console.log('[选股分发器] 测试事件定义...');
    
    // 测试所有选股事件类型
    const eventTypes = [
      SymbolSelectEvents.Types.START_STOCK_SELECTION,
      SymbolSelectEvents.Types.STOP_STOCK_SELECTION,
      SymbolSelectEvents.Types.STOCK_SELECTION_STARTED,
      SymbolSelectEvents.Types.STOCK_SELECTION_STOPPED,
      SymbolSelectEvents.Types.STOCK_SELECTION_PROGRESS,
      SymbolSelectEvents.Types.STOCK_SELECTION_COMPLETED,
      SymbolSelectEvents.Types.STOCK_SELECTION_ERROR,
    ];

    eventTypes.forEach(eventType => {
      console.log(`[选股分发器] 事件类型: ${eventType}`);
    });

    console.log('[选股分发器] 事件定义测试完成');
  }

  /**
   * 订阅选股事件
   */
  private subscribeToStockSelection() {
    // 监听前端发出的选股请求 - 绑定this上下文
    EventBus.on(SymbolSelectEvents.Types.START_STOCK_SELECTION, this.handleStartStockSelection.bind(this));
    
    // 监听停止选股请求 - 绑定this上下文
    EventBus.on(SymbolSelectEvents.Types.STOP_STOCK_SELECTION, this.handleStopStockSelection.bind(this));
    
    // 监听选股开始事件，订阅任务进度
    EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_STARTED, (payload: SymbolSelectEvents.StockSelectionStartedPayload) => {
      console.log('[选股分发器] 收到选股开始事件，订阅任务进度:', payload.taskId);
      this.currentTaskId = payload.taskId;
      
      // 如果Socket.IO已连接，立即订阅任务进度
      if (this.isConnected && this.socket) {
        console.log('[选股分发器] Socket.IO已连接，立即订阅任务进度');
        this.subscribeToTask(payload.taskId);
      } else {
        console.log('[选股分发器] Socket.IO未连接，等待连接后订阅任务进度');
      }
    });
  }

  /**
   * 订阅进度更新事件
   */
  private subscribeToProgressUpdates() {
    // 实现Redis进度监听机制
    this.setupRedisProgressListener();
    
    console.log('[选股分发器] 进度更新机制已实现');
  }

  /**
   * 设置Redis进度监听器
   */
  private setupRedisProgressListener() {
    // 监听选股进度通知
    EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_PROGRESS, (progress: SymbolSelectEvents.StockSelectionProgress) => {
      console.log('[选股分发器] 收到进度更新:', progress);
      
      // 转发进度事件到前端组件
      EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_PROGRESS, progress);
    });
  }

  /**
   * 处理开始选股请求
   * @param payload 选股配置参数
   */
  private async handleStartStockSelection(payload: SymbolSelectEvents.StartStockSelectionPayload) {
      try {
        console.log('[选股分发器] 收到选股请求:', payload);
        
        // 目前仅处理形态选股的策略类型，其他全部忽略
        if (payload.signalConfig.signalClassName !== 'ShapeSignal') {
          throw new Error('目前仅处理形态选股的策略类型，其他全部忽略');
        }
  
        // ****** Now, payload.signalConfig.name == 'ShapeSignal' ******
  
        // 创建信号实例 - 需要添加name属性以符合SignalConfig接口
        const signalConfigWithName = {
          name: payload.signalConfig.signalClassName,  // 使用signalClassName作为name
          parameters: payload.signalConfig.parameters
        };
        const signalInstance = new ShapeSignal(signalConfigWithName);
  
        // 等待形态配置加载完成
        let values: number[] = [];
        let retryCount = 0;
        const maxRetries = 10;
        
        while (retryCount < maxRetries) {
          values = signalInstance.getShapeValues();
          if (values && values.length > 0) {
            console.log('[选股分发器] 成功获取形态特征序列:', values);
            break;
          }
          
          console.log(`[选股分发器] 等待形态配置加载... (${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms
          retryCount++;
        }
        
        if (!values || values.length === 0) {
          throw new Error('无法获取形态特征序列，请检查形态配置是否正确加载');
        }
  
        // 启动选股引擎
        const token = getToken();
  
        // payload.signalCofnig.params 添加一个元素 values 记录该形态的特征序列，从 signalInstance.getShapeValues() 获取
        payload.signalConfig.parameters.values = values;
  
        // 确保threshold参数存在，从matchThreshold映射
        if (payload.signalConfig.parameters.matchThreshold !== undefined) {
          payload.signalConfig.parameters.threshold = payload.signalConfig.parameters.matchThreshold / 100; // 转换为0-1范围
        } else {
          payload.signalConfig.parameters.threshold = 0.8; // 默认阈值
        }
  
        // 调用后端api
        const response = await axios.post('/api/stock-selection/start', {
          candidate_type: payload.candidateType,
          strategy_module: 'shape_matching',
          strategy_parameters: payload.signalConfig.parameters
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
  
        if (response.data.success) {
          console.log('[选股分发器] 选股任务启动成功:', response.data);
          
          // 保存当前任务ID
          this.currentTaskId = response.data.taskId;
          
          // 如果Socket.IO已连接，立即订阅任务进度
          if (this.isConnected && this.socket) {
            this.subscribeToTask(response.data.taskId);
          }
          
          // 发送选股开始事件
          EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_STARTED, {
            taskId: response.data.taskId,
            message: '选股任务已启动'
          });
        } else {
          throw new Error(response.data.error || '启动选股任务失败');
        }
      } catch (error) {
        console.error('[选股分发器] 启动选股任务失败:', error);
        
        // 发送错误事件
        EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_ERROR, {
          message: error instanceof Error ? error.message : '启动选股任务失败'
        });
      }
    }





  /**
   * 保存选股结果到股票池管理
   * @param symbols 选中的品种列表
   */
  private async saveSelectionResultsToPool(symbols: any[]) {
    const poolName = '最新选股';
    const poolCategory = '常用'; // 对应用户/常用
    const isSystemList = false; // 用户自定义列表

    const poolItems: PoolItem[] = symbols.map((s: any) => ({
      symbol: s.code,
      name: s.name,
      data: {
        selectedAt: new Date().toISOString(),
        selectionType: 'shape_matching',
        matchScore: s.match_score, // 假设后端返回match_score
        originalSymbol: s // 保存原始数据以备用
      }
    }));

    await poolCache.updatePoolItems(poolName, isSystemList, poolItems);
    console.log(`[选股分发器] 选股结果已保存到股票池: ${poolCategory}/${poolName}`);

    // 通知股票池更新
    EventBus.emit(MarketEvents.Types.SYMBOLLIST_UPDATED, {
      listName: poolName,
      description: '最新选股结果',
      theList: poolItems,
      isSystemList: isSystemList
    });
  }

  /**
   * 处理停止选股请求
   * @param payload 停止选股参数
   */
  private async handleStopStockSelection(payload: SymbolSelectEvents.StopStockSelectionPayload) {
    try {
      console.log('[选股分发器] 收到停止选股请求:', payload);
      
      const token = getToken();
      const response = await axios.post('/api/stock-selection/stop', {
        taskId: payload.taskId
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        console.log('[选股分发器] 选股任务停止成功');
        
        // 发送选股停止事件
        EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_STOPPED, {
          taskId: payload.taskId,
          message: '选股任务已停止'
        });
      } else {
        throw new Error(response.data.error || '停止选股任务失败');
      }
    } catch (error) {
      console.error('[选股分发器] 停止选股任务失败:', error);
      
      // 发送错误事件
      EventBus.emit(SymbolSelectEvents.Types.STOCK_SELECTION_ERROR, {
        message: error instanceof Error ? error.message : '停止选股任务失败'
      });
    }
  }

  /**
   * 获取选股结果
   * @param taskId 任务ID
   */
  async getStockSelectionResult(taskId: string): Promise<SymbolSelectEvents.StockSelectionResult> {
    try {
      const token = getToken();
      const response = await axios.get(`/api/stock-selection/result/${taskId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error || '获取选股结果失败');
      }
    } catch (error) {
      console.error('[选股分发器] 获取选股结果失败:', error);
      throw error;
    }
  }

  /**
   * 获取选股任务状态
   * @param taskId 任务ID
   */
  async getStockSelectionStatus(taskId: string): Promise<SymbolSelectEvents.StockSelectionStatus> {
    try {
      const token = getToken();
      const response = await axios.get(`/api/stock-selection/status/${taskId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error || '获取选股状态失败');
      }
    } catch (error) {
      console.error('[选股分发器] 获取选股状态失败:', error);
      throw error;
    }
  }

  /**
   * 监听选股进度更新
   * @param callback 进度回调函数
   */
  onProgressUpdate(callback: (progress: SymbolSelectEvents.StockSelectionProgress) => void) {
    return EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_PROGRESS, callback);
  }

  /**
   * 监听选股结果
   * @param callback 结果回调函数
   */
  onSelectionResult(callback: (result: SymbolSelectEvents.StockSelectionResult) => void) {
    return EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_COMPLETED, callback);
  }

  /**
   * 监听选股错误
   * @param callback 错误回调函数
   */
  onSelectionError(callback: (error: SymbolSelectEvents.StockSelectionError) => void) {
    return EventBus.on(SymbolSelectEvents.Types.STOCK_SELECTION_ERROR, callback);
  }
}

// 创建单例实例
export const symbolSelectDispatcher = new SymbolSelectDispatcher();

// 导出初始化函数
export function initializeSymbolSelectDispatcher() {
  symbolSelectDispatcher.initialize();
  // 测试事件定义
  symbolSelectDispatcher.testEventDefinitions();
}

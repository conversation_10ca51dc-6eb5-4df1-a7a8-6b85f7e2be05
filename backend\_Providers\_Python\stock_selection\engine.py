import importlib.util
import json
import time
import redis
import os
import requests
from typing import List, Dict, Any, Callable, Optional
import sys
import codecs

# 信号冗余
Signal_Redundancy = 4

# 设置UTF-8编码环境
os.environ['PYTHONIOENCODING'] = 'utf-8'
# 修复Windows下的编码问题
if sys.platform.startswith('win'):
    try:
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
    except AttributeError:
        # 如果buffer不存在，使用其他方法
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 添加启动日志
print("[选股引擎] ========== 开始启动 ==========")
print(f"[选股引擎] 当前工作目录: {os.getcwd()}")
print(f"[选股引擎] Python版本: {sys.version}")
print(f"[选股引擎] 脚本路径: {__file__}")
print(f"[选股引擎] 命令行参数: {sys.argv}")
print("[选股引擎] ================================")

class StockSelectionEngine:
    def __init__(self, config: Dict[str, Any]):
        """
        初始化选股引擎
        
        Args:
            config: 配置字典，包含：
                - test_mode: 是否为测试模式
                - candidate_type: 候选品种类型 (0=A股, 1=期货, 2=美股)
                - strategy_module: 策略模块名称
                - strategy_params: 策略参数字典
                - threshold: 阈值
                - values: 特征序列
                - progress_callback: 进度回调函数 (可选)
                - task_id: 任务ID (用于Redis进度通知)
                - tdx_host: TDX主机
                - tdx_port: TDX端口
                - redis_host: Redis主机
                - redis_port: Redis端口
                - redis_password: Redis密码
                - redis_db: Redis数据库
        """
        # 测试模式标志
        self.test_mode = config.get('test_mode', False)
        
        self.candidate_type = config.get('candidate_type', 0)
        self.strategy_module_path = config.get('strategy_module')

        self.threshold = config.get('threshold', 0)
        self.values = config.get('values', [])
        # Debug prints added in __init__
        print(f"[选股引擎] 接收到的参数:")
        print(f"  - test_mode: {self.test_mode}")
        print(f"  - candidate_type: {self.candidate_type}")
        print(f"  - strategy_module_path: {self.strategy_module_path}")
        print(f"  - threshold: {self.threshold}")
        print(f"  - values: {self.values}")
        print(f"  - values类型: {type(self.values)}")
        print(f"  - values长度: {len(self.values) if self.values else 0}")
        

        self.fetch_k_count = len(self.values) + Signal_Redundancy

        self.tdx_host = config.get('tdx_host', 'localhost')
        self.tdx_port = config.get('tdx_port', 5000)

        # Redis配置
        self.task_id = config.get('task_id')
        self.redis_host = config.get('redis_host', 'localhost')
        self.redis_port = config.get('redis_port', 6379)
        self.redis_password = config.get('redis_password')
        self.redis_db = config.get('redis_db', 0)
        
        # 初始化Redis连接
        self.redis_client = None
        if self.task_id:
            try:
                self.redis_client = redis.StrictRedis(
                    host=self.redis_host,
                    port=self.redis_port,
                    password=self.redis_password,
                    db=self.redis_db,
                    decode_responses=True
                )
                print(f"[选股引擎] Redis连接成功: {self.redis_host}:{self.redis_port}")
            except Exception as e:
                print(f"[选股引擎] Redis连接失败: {e}")
        
        # 动态加载策略模块
        self.strategy_module = self.load_strategy_module()
        
        # API配置（从config.json获取）
        self.api_base_url = f"http://{self.tdx_host}:{self.tdx_port}"
        
        print(f"[选股引擎] API服务地址: {self.api_base_url}")
        
        # 进度状态
        self.total_symbols = 0
        self.processed_symbols = 0
        self.selected_symbols = []
        self.last_progress_percent = 0  # 记录上次报告的进度百分比
        
    def load_strategy_module(self):
        """动态加载策略模块"""
        if not self.strategy_module_path:
            raise ValueError("策略模块路径未指定")

        # 策略模块名称映射
        strategy_name_mapping = {
            'shape_matching': 'pattern_strategy.py',
            'pattern_strategy': 'pattern_strategy.py',
            'ma_cross': 'ma_cross_strategy.py'
        }
        
        # 根据策略模块名称获取文件名
        strategy_name = self.strategy_module_path
        if strategy_name in strategy_name_mapping:
            strategy_name = strategy_name_mapping[strategy_name]
        elif not strategy_name.endswith('.py'):
            strategy_name += '.py'
        
        # 构建完整的文件路径
        strategies_dir = os.path.join(os.path.dirname(__file__), 'strategies')
        module_path = os.path.join(strategies_dir, strategy_name)
        
        if not os.path.exists(module_path):
            raise FileNotFoundError(f"策略模块文件不存在: {module_path}")

        try:
            # 动态导入策略模块
            spec = importlib.util.spec_from_file_location(
                "strategy_module", 
                module_path
            )
            if spec is None:
                raise RuntimeError("无法创建模块规范")

            strategy_module = importlib.util.module_from_spec(spec)
            if spec.loader is None:
                raise RuntimeError("模块加载器为空")

            spec.loader.exec_module(strategy_module)

            # 验证策略模块接口
            if not hasattr(strategy_module, 'select_stock'):
                raise ValueError("策略模块必须包含 select_stock 函数")

            return strategy_module

        except Exception as e:
            raise RuntimeError(f"加载策略模块失败: {str(e)}")
    
    def report_progress(self, stage: str, current: Optional[int] = None, total: Optional[int] = None, 
                       selected_count: Optional[int] = None, symbol_info: Optional[str] = None):
        """报告进度"""
        # 计算进度百分比
        progress_percent = 0
        if total and total > 0:
            progress_percent = int((current or 0) * 100 / total)
        
        # 检查是否需要发送Redis进度通知（每1%发送一次，或者首次发送）
        should_send_redis = False
        if progress_percent > self.last_progress_percent or (current and current <= 1):
            should_send_redis = True
            self.last_progress_percent = progress_percent
        
        # 构建进度数据
        progress_data = {
            'taskId': self.task_id,
            'stage': stage,
            'current': current,
            'total': total,
            'selectedCount': selected_count,
            'symbolInfo': symbol_info,
            'progress': progress_percent,
            'timestamp': time.time()
        }
        
        # 发送Redis进度通知
        if should_send_redis and self.redis_client and self.task_id:
            try:
                channel = f'stock_selection_progress:{self.task_id}'
                self.redis_client.publish(channel, json.dumps(progress_data))
                print(f"[选股引擎] 发送Redis进度通知: {progress_percent}% - {stage}")
                print(f"[选股引擎] Redis频道: {channel}")
                print(f"[选股引擎] Redis数据: {json.dumps(progress_data)}")
            except Exception as e:
                print(f"[选股引擎] 发送Redis进度通知失败: {e}")
        else:
            print(f"[选股引擎] 跳过Redis进度通知 - should_send_redis: {should_send_redis}, redis_client: {self.redis_client is not None}, task_id: {self.task_id}")
        
        # 调用原有的进度回调
        #if self.progress_callback:
        #    self.progress_callback(progress_data)
        # 使用 Redis 方式更新进度，不使用回调
        
        # 打印进度信息
        if current and total:
            print(f"[选股引擎] 进度: {progress_percent}% ({current}/{total}) - {stage}")
        else:
            print(f"[选股引擎] 进度: {stage}")
    
    def get_candidate_symbols(self) -> List[Dict[str, Any]]:
        """根据候选类型获取候选品种列表"""
        try:
            self.report_progress('获取候选品种列表')
            
            if self.candidate_type == 0:  # A股
                url = f"{self.api_base_url}/stock/list"
                params = {'market': 'a'}  # 只获取A股
            elif self.candidate_type == 1:  # 期货
                url = f"{self.api_base_url}/future/list"
                params = {}
            elif self.candidate_type == 2:  # 美股
                url = f"{self.api_base_url}/us/list"
                params = {}
            else:
                raise ValueError(f"不支持的候选类型: {self.candidate_type}")
            
            print(f"[选股引擎] 请求API: {url}")
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            response_data = response.json()
            symbols = response_data.get('data', []) if response_data.get('success') else []
            
            self.total_symbols = len(symbols)
            print(f"[选股引擎] 获取到 {self.total_symbols} 个候选品种")
            
            return symbols
            
        except Exception as e:
            print(f"[选股引擎] 获取候选品种失败: {str(e)}")
            return []
    

    
    def get_klines(self, symbol: Dict[str, Any], period: str = '1D') -> List[Dict[str, Any]]:
        """获取品种K线数据"""
        try:
            # 确定需要的K线数量
            required_klines = self.fetch_k_count
            
            # 构建API请求参数
            params = {
                'symbol': symbol['code'],
                'period': period,
                'market': symbol.get('market', 'STOCK'),
                'exchange': symbol.get('exchange', ''),
                'k_count': required_klines
            }
            
            # 根据市场类型选择API端点
            if symbol.get('market') == 'FUTURE':
                url = f"{self.api_base_url}/future/kline"
            else:
                url = f"{self.api_base_url}/stock/kline"
            
            print(f"[选股引擎] 请求K线数据: {url}, 参数: {params}")
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            response_data = response.json()
            klines = response_data.get('data', []) if response_data.get('success') else []
            
            return klines
                
        except Exception as e:
            print(f"[选股引擎] 获取K线数据失败 {symbol['code']}: {str(e)}")
            return []

    def run_test_mode(self) -> List[str]:
        """运行测试模式 - 模拟进度更新从1%到100%"""
        import time

        print("[选股引擎] 进入测试模式，开始模拟进度更新")

        # 模拟总共100个品种需要处理
        total_symbols = 100
        selected_count = 0

        # 模拟选中的品种
        test_symbols = [
            {'code': '000001', 'name': '平安银行'},
            {'code': '000002', 'name': '万科A'},
            {'code': '000858', 'name': '五粮液'},
            {'code': '600036', 'name': '招商银行'},
            {'code': '600519', 'name': '贵州茅台'}
        ]

        # 从1%到100%，每秒递增1%
        for progress in range(1, 101):
            current_symbol = progress

            # 每20%选中一个品种
            if progress % 20 == 0 and selected_count < len(test_symbols):
                selected_symbol = test_symbols[selected_count]
                selected_count += 1
                symbol_info = f"{selected_symbol['code']} - {selected_symbol['name']} (已选中)"
                print(f"[选股引擎] 测试模式选中品种: {symbol_info}")
            else:
                symbol_info = f"TEST{progress:03d} - 测试品种{progress}"

            # 报告进度
            self.report_progress(
                stage=f'测试进度更新 ({progress}%)',
                current=current_symbol,
                total=total_symbols,
                selected_count=selected_count,
                symbol_info=symbol_info
            )

            print(f"[选股引擎] 测试模式进度: {progress}% - {symbol_info}")

            # 等待1秒
            time.sleep(1)

        # 最终报告完成
        self.report_progress(
            stage='测试完成',
            current=total_symbols,
            total=total_symbols,
            selected_count=selected_count,
            symbol_info='测试模式完成'
        )

        print(f"[选股引擎] 测试模式完成，共选中 {selected_count} 个品种")

        # 返回选中的品种代码
        return [symbol['code'] for symbol in test_symbols[:selected_count]]

    def run_selection(self) -> List[str]:
        """执行选股轮询"""
        print(f"[选股引擎] 开始执行选股，候选类型: {self.candidate_type}")
        
        # 检查是否为测试模式
        if self.test_mode:
            return self.run_test_mode()
        
        self.report_progress('开始选股')
        
        # 获取候选品种
        symbols = self.get_candidate_symbols()
        print(f"[选股引擎] 获取到 {len(symbols)} 个候选品种")
        
        self.selected_symbols = []
        self.processed_symbols = 0
        self.last_progress_percent = 0  # 重置进度百分比
        
        # 发送初始进度更新（0%）
        if symbols:
            self.report_progress('开始处理品种', current=0, total=len(symbols))
        for symbol in symbols:
            try:
                
                # 获取K线数据
                klines = self.get_klines(symbol)
                if not klines:
                    continue
                
                print(f"[选股引擎] 特征序列： {self.values}")

                # 调用策略
                if self.strategy_module.select_stock(klines, self.threshold, self.values):
                    self.selected_symbols.append(symbol)
                    print(f"[选股引擎] 选中品种: {symbol['code']} - {symbol.get('name', '')}")
                    
                self.processed_symbols += 1
                # 报告进度
                self.report_progress(
                    '处理品种',
                    current=self.processed_symbols,
                    total=len(symbols),
                    selected_count=len(self.selected_symbols),
                    symbol_info=f"{symbol['code']} - {symbol.get('name', '')}"
                )
                

            except Exception as e:
                print(f"[选股引擎] 处理品种 {symbol['code']} 时出错: {str(e)}")
                continue
        
        print(f"[选股引擎] 选股完成，共选中 {len(self.selected_symbols)} 个品种")
        self.report_progress('选股完成', 
                           current=len(symbols), 
                           total=len(symbols), 
                           selected_count=len(self.selected_symbols))
        
        return self.selected_symbols


    
    def get_selection_result(self) -> Dict[str, Any]:
        """获取选股结果"""
        return {
            'total_symbols': self.total_symbols,
            'processed_symbols': self.processed_symbols,
            'selected_symbols': self.selected_symbols,
            'selected_count': len(self.selected_symbols)
        }


# 主程序逻辑
if __name__ == '__main__':
    print("[选股引擎] 开始解析命令行参数...")
    
    try:
        # 读取config.json配置文件
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'config.json')
        print(f"[选股引擎] 配置文件路径: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            global_config = json.load(f)
        
        # 获取python_service配置（app.py运行在这个端口）
        python_service_config = global_config.get('python_service', {})
        tdx_host = python_service_config.get('host', 'localhost')
        tdx_port = python_service_config.get('port', 5000)
        
        # 获取 Redis 配置
        redis_config = global_config.get('redis', {})
        redis_host = redis_config.get('host', 'localhost')
        redis_port = redis_config.get('port', 6379)
        redis_password = redis_config.get('password', None)
        redis_db = redis_config.get('db', 0)

        print(f"[选股引擎] 从config.json获取API配置: {tdx_host}:{tdx_port}")
    
        # 如果有命令行参数，解析JSON配置
        param_json = sys.argv[1]
        parameter = json.loads(param_json)
        print(f"[选股引擎] 从命令行参数获取配置: {parameter}")
        
        # 添加API配置
        parameter['tdx_host'] = tdx_host
        parameter['tdx_port'] = tdx_port
        parameter['redis_host'] = redis_host
        parameter['redis_port'] = redis_port
        parameter['redis_password'] = redis_password
        parameter['redis_db'] = redis_db
        
        # 创建选股引擎实例
        print("[选股引擎] 创建选股引擎实例...")
        engine = StockSelectionEngine(parameter)
        
        # 执行选股
        print("[选股引擎] 开始执行选股...")
        selected_symbols = engine.run_selection()
        
        # 输出结果
        result = engine.get_selection_result()
        print(f"[选股引擎] 选股完成，结果: {result}")
        
        # 将结果输出到stdout，供Node.js读取
        print(f"RESULT:{json.dumps(result)}")
        
    except Exception as e:
        print(f"[选股引擎] 执行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 
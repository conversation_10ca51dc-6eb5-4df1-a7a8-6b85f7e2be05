import { EventBus } from '../events/eventBus';
import httpDispatcher from './HttpDispatcher';
import { getToken, getUserInfo } from '@/utils/auth';
import { jotaiStore } from '@/store/state';
import { MarketEvents } from '@/events/events';
import axios from 'axios';
import { poolCache, PoolItem } from '@/utils/PoolCache';

// --- 新增：定义接口 ---
// 定义池子接口，与后端数据同步
interface Pool {
  pool_id?: number;
  name: string;
  description?: string;
  is_public?: boolean;
  is_system?: boolean;
  symbols_json?: any;
  user_id?: number;
}

// 定义分类接口
interface Category {
  category_id?: number;
  name: string;
  description?: string;
  icon?: string;
  sort_order?: number;
  type?: string;
  user_id?: number;
  pools_json?: string;
}

// 符号列表项接口
interface SymbolListItem {
  symbol: Symbol;
  data: any;  // json格式，例如 { viewedAt: string, notes: string }
}

const poolDispatcher = {
  initialized: false,
  subscribedSymbolListEvents: false,
  symbolListCache: new Map<string, SymbolListItem[]>(), // 内存缓存列表

  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      // 初始化本地缓存
      await poolCache.initialize();

      // 订阅事件
      this.subscribeToSymbolListEvents();

      this.initialized = true;
      console.log('[股票池] 初始化完成');
    } catch (error) {
      console.error('[股票池] 初始化失败:', error);
      throw error;
    }
  },

  // 获取所有分类
  async getAllCategories() {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法获取分类数据');

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.get('/pool/categories', {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.success) {
          console.log(`[股票池] 成功获取所有分类数据`);
          return response.data.data;
        } else {
          console.error(`[股票池] 获取分类数据失败:`, response.data.error || response.data.message || '未知错误');
          return null; // Return null on logical failure
        }
      } catch (apiError) {
         console.error(`[股票池] 调用获取分类API时失败:`, apiError);
         // Check if it's an Axios error (like 401/403)
         if (axios.isAxiosError(apiError)) {
             console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
             // Interceptor handles logout for 401/403.
         }
         return null; // Return null on API call failure
      }
      // --- End wrap ---
    } catch (error) {
      // Error likely from getToken or initial checks
      console.error(`[股票池] 获取分类数据前置检查失败:`, error);
      // Re-throw only if it's critical, otherwise return null/handle gracefully
      // For now, let's return null to avoid crashing caller
      return null;
    }
  },

  // 获取特定分类详情
  async getCategoryById(categoryId: number) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法获取分类详情');

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.get(`/pool/categories/${categoryId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.success) {
          console.log(`[股票池] 成功获取分类详情: ${categoryId}`);
          return response.data.data;
        } else {
          console.error(`[股票池] 获取分类详情失败:`, response.data.error || response.data.message || '未知错误');
          return null;
        }
      } catch (apiError) {
        console.error(`[股票池] 调用获取分类详情API (${categoryId}) 时失败:`, apiError);
        if (axios.isAxiosError(apiError)) {
          console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
        }
        return null;
      }
      // --- End wrap ---
    } catch (error) {
      console.error(`[股票池] 获取分类详情 (${categoryId}) 前置检查失败:`, error);
      return null;
    }
  },

  // 创建新分类
  async createCategory(category: Category) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法创建分类');

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.post('/pool/categories', category, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.success) {
          console.log(`[股票池] 成功创建分类: ${category.name}`);
          return response.data.data;
        } else {
          console.error(`[股票池] 创建分类失败:`, response.data.error || response.data.message || '未知错误');
          return null;
        }
      } catch (apiError) {
        console.error(`[股票池] 调用创建分类API (${category.name}) 时失败:`, apiError);
        if (axios.isAxiosError(apiError)) {
          console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
        }
        return null;
      }
      // --- End wrap ---
    } catch (error) {
      console.error(`[股票池] 创建分类 (${category.name}) 前置检查失败:`, error);
      return null;
    }
  },

  // 更新分类信息
  async updateCategory(categoryId: number, category: Partial<Category>) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法更新分类');

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.put(`/pool/categories/${categoryId}`, category, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.success) {
          console.log(`[股票池] 成功更新分类: ${categoryId}`);
          return response.data.data;
        } else {
          console.error(`[股票池] 更新分类失败:`, response.data.error || response.data.message || '未知错误');
          return null;
        }
      } catch (apiError) {
        console.error(`[股票池] 调用更新分类API (${categoryId}) 时失败:`, apiError);
        if (axios.isAxiosError(apiError)) {
          console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
        }
        return null;
      }
      // --- End wrap ---
    } catch (error) {
      console.error(`[股票池] 更新分类 (${categoryId}) 前置检查失败:`, error);
      return null;
    }
  },

  // 删除分类
  async deleteCategory(categoryId: number) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法删除分类');

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.delete(`/pool/categories/${categoryId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.success) {
          console.log(`[股票池] 成功删除分类: ${categoryId}`);
          return true;
        } else {
          console.error(`[股票池] 删除分类失败:`, response.data.error || response.data.message || '未知错误');
          return false;
        }
      } catch (apiError) {
        console.error(`[股票池] 调用删除分类API (${categoryId}) 时失败:`, apiError);
        if (axios.isAxiosError(apiError)) {
          console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
        }
        return false;
      }
      // --- End wrap ---
    } catch (error) {
      console.error(`[股票池] 删除分类 (${categoryId}) 前置检查失败:`, error);
      return false;
    }
  },

  // 更新分类中的池子列表
  async updateCategoryPools(categoryId: number, pools: Array<{ pool_id: number, pool_name: string }>) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法更新分类池列表');

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.put(`/pool/categories/${categoryId}/pools`, { pools }, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.success) {
          console.log(`[股票池] 成功更新分类池列表: ${categoryId}`);
          return response.data.data;
        } else {
          console.error(`[股票池] 更新分类池列表失败:`, response.data.error || response.data.message || '未知错误');
          return null;
        }
      } catch (apiError) {
        console.error(`[股票池] 调用更新分类池列表API (${categoryId}) 时失败:`, apiError);
        if (axios.isAxiosError(apiError)) {
          console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
        }
        return null;
      }
      // --- End wrap ---
    } catch (error) {
      console.error(`[股票池] 更新分类池列表 (${categoryId}) 前置检查失败:`, error);
      return null;
    }
  },

  // 获取特定池子详情
  async getPoolByPoolId(poolId: number) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法获取池子详情');

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.get(`/pool/pools/${poolId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.success) {
          console.log(`[股票池] 成功获取池子详情: ${poolId}`);
          return response.data.data;
        } else {
          console.error(`[股票池] 获取池子详情失败:`, response.data.error || response.data.message || '未知错误');
          return null;
        }
      } catch (apiError) {
        console.error(`[股票池] 调用获取池子详情API (${poolId}) 时失败:`, apiError);
        if (axios.isAxiosError(apiError)) {
          console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
        }
        return null;
      }
      // --- End wrap ---
    } catch (error) {
      console.error(`[股票池] 获取池子详情 (${poolId}) 前置检查失败:`, error);
      return null;
    }
  },

  // 创建新池子
  // 返回新创建池子的 id 或 null
  async createPool(pool: Pool) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法创建池子');

      // 获取用户ID (check if needed for createPool's logic later)
      const userInfo = getUserInfo();
      if (!userInfo || !userInfo.id) {
        throw new Error('无法获取用户ID，无法创建池子');
      }

      // 直接调用 updateSymbolList 来处理创建逻辑
      console.log(`[股票池] 调用 updateSymbolList 创建新池子: ${pool.name}`);
      const result = await this.updateSymbolList(pool);

      if (result && result.id) { // Check if result is valid and has an ID
        console.log(`[股票池] 成功创建池子: ${pool.name}, ID: ${result.id}`);
        return result; // Return the result object containing ID, etc.
      } else {
        console.error(`[股票池] 创建池子 ${pool.name} 失败 (updateSymbolList 返回 null 或无效结果)`);
        return null;
      }
    } catch (error) {
      console.error(`[股票池] 创建池子 (${pool.name}) 时发生外部错误:`, error);
      // Don't re-throw, return null to indicate failure
      return null;
    }
  },

  // 更新池子
  async updatePool(poolId: number, pool: Partial<Pool>) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法更新池子');

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.put(`/pool/pools/${poolId}`, pool, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.success) {
          console.log(`[股票池] 成功更新池子: ${poolId}`);
          // Optionally update local cache if needed
          return response.data.data;
        } else {
          console.error(`[股票池] 更新池子失败:`, response.data.error || response.data.message || '未知错误');
          return null;
        }
      } catch (apiError) {
        console.error(`[股票池] 调用更新池子API (${poolId}) 时失败:`, apiError);
        if (axios.isAxiosError(apiError)) {
          console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
        }
        return null;
      }
      // --- End wrap ---
    } catch (error) {
      console.error(`[股票池] 更新池子 (${poolId}) 前置检查失败:`, error);
      return null;
    }
  },

  // 删除池子 (实际是删除SymbolList)
  async deletePool(poolId: number, listName: string, isSystemList: boolean) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法删除列表');

      console.log(`[股票池] 准备删除列表: name=${listName}, isSystem=${isSystemList}`);

      if (!this.initialized) await this.initialize();

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.delete('/pool/symbollist', {
          headers: { 'Authorization': `Bearer ${token}` },
          params: { listName: listName, isSystemList: isSystemList }
        });

        if (response.data.success) {
          console.log(`[股票池] 成功删除列表: ${listName}`);
          // Clear cache after successful deletion
          try {
            await poolCache.clearPoolCache(listName, isSystemList);
            console.log(`[股票池] 已清除本地缓存中的列表 ${listName}`);
          } catch (cacheError) {
            console.warn(`[股票池] 清除本地缓存中的列表 ${listName} 失败:`, cacheError);
          }
          return true;
        } else {
          console.error(`[股票池] 删除列表 ${listName} 失败:`, response.data.error || response.data.message || '未知错误');
          return false;
        }
      } catch (apiError) {
        console.error(`[股票池] 调用删除列表API (${listName}) 时失败:`, apiError);
        if (axios.isAxiosError(apiError)) {
          console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
          // Log additional details if available
          // console.error(`[股票池] Request URL: ${apiError.config?.url}, Params: ${JSON.stringify(apiError.config?.params)}`);
        }
        return false; // Indicate failure
      }
      // --- End wrap ---
    } catch (error) {
      console.error(`[股票池] 删除列表 ${listName} 前置检查或初始化失败:`, error);
      // Depending on the error, might want to return false or re-throw
      return false;
    }
  },

  subscribeToSymbolListEvents() {
    if (this.subscribedSymbolListEvents) {
      console.log('[股票池] 列表相关事件已订阅，跳过');
      return;
    }
    console.log('[股票池] 开始订阅列表相关事件...');

    // Listener for REQUEST_SYMBOLLIST_DATA
    EventBus.on(MarketEvents.Types.REQUEST_SYMBOLLIST_DATA, async (payload: MarketEvents.RequestSymbolListDataPayload) => {
      const { symbolListName, isSystemList, tag } = payload; // Destructure tag
      console.log(`[股票池] 事件请求: ${JSON.stringify(payload)}`);
      try {
        const token = getToken();
        if (!token) throw new Error('用户未登录，无法获取列表数据');

        // --- Wrap API call ---
        try {
          const response = await httpDispatcher.get('/pool/symbollist', {
            params: { listName: symbolListName, isSystemList: isSystemList },
            headers: { 'Authorization': `Bearer ${token}` }
          });

          if (response.data.success) {
            console.log(`[股票池] 成功获取列表 ${response.data.listName} 的原始数据`);
            EventBus.emit(MarketEvents.Types.SYMBOLLIST_CHANGED, {
              symbolListName: symbolListName,
              theList: response.data.data || [], // Ensure theList is always an array
              description: response.data.description,
              isSystemList: isSystemList,
              tag: tag // Pass tag along
            });
          } else {
            console.error(`[股票池] 获取列表 ${symbolListName} 失败:`, response.data.error || '未知错误');
            // Optionally emit an error event or handle failure
          }
        } catch (apiError) {
          console.error(`[股票池] 调用获取列表API (${symbolListName}) 时失败:`, apiError);
          if (axios.isAxiosError(apiError)) {
             console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
          }
          // Optionally emit an error event
        }
        // --- End wrap ---
      } catch (error) {
        console.error(`[股票池] 处理请求列表 ${symbolListName} 事件时失败:`, error);
      }
    });

    // Listener for UPDATE_SYMBOLLIST
    EventBus.on(MarketEvents.Types.UPDATE_SYMBOLLIST, async (payload: MarketEvents.UpdateSymbolListPayload) => {
      const { listName, theList, isSystemList, description } = payload;
      console.log(`[股票池] 收到更新列表事件请求: ${listName}, 是否系统列表: ${isSystemList}`);
      try {
        const token = getToken();
        if (!token) throw new Error('用户未登录，无法更新列表');

        // --- Wrap API call ---
        try {
          const response = await httpDispatcher.post('/pool/updatesymbollist', {
            listName,
            theList,
            isSystemList,
            description,
          }, {
            headers: { 'Authorization': `Bearer ${token}` }
          });

          if (response.data.success) {
            console.log(`[股票池] 后端成功更新列表 ${listName}.`);
             // Optionally trigger a re-fetch or update local state if needed
          } else {
            console.error(`[股票池] 后端更新列表 ${listName} 失败:`, response.data.error || '未知错误');
             // Optionally show error message to user
          }
        } catch (apiError) {
          console.error(`[股票池] 调用更新列表API (${listName}) 时失败:`, apiError);
          if (axios.isAxiosError(apiError)) {
             console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
          }
           // Optionally show error message to user
        }
        // --- End wrap ---
      } catch (error) {
        console.error(`[股票池] 处理更新列表 ${listName} 事件时失败:`, error);
      }
    });

    this.subscribedSymbolListEvents = true;
    console.log('[股票池] 列表相关事件订阅完成');
  },

  // 直接获取符号列表（不通过事件总线）
  async getSymbolList(symbolListName: string, isSystemList: boolean = false) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法获取列表数据');

      if (!this.initialized) await this.initialize();

      // Try cache first (using public method)
      console.log(`[股票池] 尝试从本地缓存获取列表 ${symbolListName}`);
      let items = await poolCache.getPoolItems(symbolListName, isSystemList);
      // We cannot reliably get poolId from cache via public methods currently
      // let cachedPoolId = 0; // Removed attempt to get cachedPoolId

      if (items && items.length > 0) {
        console.log(`[股票池] 成功从缓存获取列表 ${symbolListName}，共 ${items.length} 项`);
        console.log(`[股票池] 缓存数据示例:`, items[0]);
        // Return without poolId if from cache, or consider adding a method to PoolCache to get metadata
        return { id: 0, listName: symbolListName, data: items, description: '' };
      }

      // Fetch from backend if cache miss
      console.log(`[股票池] 缓存未命中或为空，从后端获取 ${symbolListName}`);
      const fetchFromBackend = async () => {
          // --- Wrap API call ---
          try {
              const response = await httpDispatcher.get('/pool/symbollist', {
                  params: { listName: symbolListName, isSystemList },
                  headers: { 'Authorization': `Bearer ${token}` }
              });

              if (response.data.success) {
                  // Ensure the structure matches what fetchAndCachePoolItems expects
                  const poolId = response.data.poolId || response.data.id || 0;
                  // Make sure to return the full expected structure including poolId
                  return {
                      success: true,
                      data: response.data.data || [],
                      poolId: poolId,
                      description: response.data.description || ''
                  };
              } else {
                  console.error(`[股票池] 获取列表 ${symbolListName} API失败:`, response.data.error || '未知错误');
                  return { success: false, data: [], poolId: 0, description: '' }; // Return structure indicating failure
              }
          } catch (apiError) {
              console.error(`[股票池] 调用获取列表API (${symbolListName}) 时失败:`, apiError);
              if (axios.isAxiosError(apiError)) {
                  console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
              }
              return { success: false, data: [], poolId: 0, description: '' }; // Return structure indicating failure
          }
          // --- End wrap ---
      };

      // Use fetchAndCachePoolItems - it handles saving internally
      // Pass the fetch function directly. fetchAndCachePoolItems will call it.
      const fetchedItems = await poolCache.fetchAndCachePoolItems(symbolListName, isSystemList, fetchFromBackend);

      // fetchAndCachePoolItems returns the items array directly, or empty array on failure.
      // We need the poolId and description from the backend call as well.
      // Let's re-call fetchFromBackend to get the full metadata (slightly redundant but needed)
      // Alternatively, modify fetchAndCachePoolItems to return more info or handle caching differently.
      const backendResult = await fetchFromBackend();

      if (backendResult && backendResult.success) {
        console.log(`[股票池] 成功从后端获取并缓存列表 ${symbolListName}，共 ${fetchedItems.length} 项, ID=${backendResult.poolId}`);
        return {
          id: backendResult.poolId,
          listName: symbolListName,
          data: fetchedItems, // Use items returned by fetchAndCachePoolItems
          description: backendResult.description
        };
      } else {
        // If fetch failed (indicated by backendResult.success === false or backendResult being null)
        console.log(`[股票池] 后端获取列表 ${symbolListName} 失败，返回空列表`);
        return { id: 0, listName: symbolListName, data: [], description: '', error: 'Failed to fetch from backend' };
      }

    } catch (error) {
      console.error(`[股票池] 获取列表 ${symbolListName} 时发生外部错误:`, error);
      return { id: 0, listName: symbolListName, data: [], description: '', error: error instanceof Error ? error.message : String(error) };
    }
  },

  // 直接更新符号列表（不通过事件总线）
  // 修改原列表或者创建新列表
  // Returns an object { id, listName, isSystemList, isCreated } or null on failure
  async updateSymbolList(pool: Pool): Promise<{ id: number; listName: string; isSystemList: boolean; isCreated: boolean; } | null> {
    try {
      const { name: listName, symbols_json: theList = [], is_system: isSystemList = false, description = '', pool_id } = pool;

      const token = getToken();
      if (!token) throw new Error('用户未登录，无法更新列表');

      if (!this.initialized) await this.initialize();

      console.log(`[股票池] 准备更新/创建列表 ${listName} (传入池ID: ${pool_id})`);

      let responseData: any = null; // To store successful response data

      // --- Wrap API call ---
      try {
          const response = await httpDispatcher.post('/pool/updatesymbollist', {
              listName,
              theList,
              isSystemList,
              description,
              poolId: pool_id // Send pool_id if available
          }, {
              headers: { 'Authorization': `Bearer ${token}` }
          });

          if (!response.data.success) {
              console.error(`[股票池] 通过API更新/创建列表 ${listName} 失败:`, response.data.error || '未知错误');
              return null; // Indicate failure
          }

          console.log(`[股票池] 成功通过API更新/创建列表 ${listName}，响应:`, response.data);
          responseData = response.data; // Store successful response

      } catch (apiError) {
          console.error(`[股票池] 调用更新列表API (${listName}) 时失败:`, apiError);
          if (axios.isAxiosError(apiError)) {
              console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
          }
          return null; // Indicate failure
      }
      // --- End wrap ---

      // Process successful response (responseData is not null here)
      let poolId = 0;
      let returnedName = listName;
      let isCreated = false;

      if (responseData.data && typeof responseData.data === 'object') {
          poolId = responseData.data.id || responseData.data.pool_id || pool_id || 0; // Use response ID first, then input ID
          returnedName = responseData.data.listName || responseData.data.name || listName;
          isCreated = responseData.message && responseData.message.includes('created');
          console.log(`[股票池] 从响应提取信息: ID=${poolId}, 名称=${returnedName}, 新创建=${isCreated}`);
      } else {
          // Fallback if response.data is missing or not an object
          poolId = pool_id || 0;
          console.log(`[股票池] 响应中无有效data，使用传入ID: ${poolId}`);
      }

      // Update local cache after successful API call
      try {
          console.log(`[股票池] 更新本地缓存: 列表=${returnedName}, ID=${poolId}`);
          await poolCache.updatePoolItems(returnedName, isSystemList, theList || [], poolId);
          console.log(`[股票池] 本地缓存更新成功`);
      } catch (cacheError) {
          console.error(`[股票池] 更新本地缓存失败:`, cacheError);
      }

      return {
          id: poolId,
          listName: returnedName,
          isSystemList: isSystemList,
          isCreated: isCreated,
          // Include the raw response data if needed by caller
          // rawData: responseData.data
      };

    } catch (error) {
      // Error likely from getToken or initialize
      console.error(`[股票池] 更新列表前置检查或初始化失败:`, error);
      // Instead of re-throwing, return null to match expected return type on failure
      return null;
    }
  },

  /**
   * 更新指定池子中的单个Symbol信息
   * @param poolId 池子的ID
   * @param symbol Symbol代码 (格式: 交易所.类型.代码)
   * @param name Symbol名称 (仅当Symbol不存在时需要提供)
   * @param data 要更新或设置的附加数据
   * @returns 更新后的 symbols_json 数组，失败返回 null
   */
  async updatePoolSymbol(poolId: number, symbol: string, name?: string, data?: any) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法更新池子中的Symbol');

      console.log(`[股票池] 请求更新池 ${poolId} 的 Symbol: ${symbol}`);

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.put(`/pool/pools/${poolId}/symbol`, {
          symbol,
          name,
          data
        }, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.success) {
          console.log(`[股票池] 成功更新池 ${poolId} 的 Symbol ${symbol}`);
          // Optionally update local cache here if needed
          return response.data.data;
        } else {
          console.error(`[股票池] 更新池 ${poolId} 的 Symbol ${symbol} 失败:`, response.data.message || '未知错误');
          return null;
        }
      } catch (apiError) {
        console.error(`[股票池] 调用更新Symbol API (池 ${poolId}, Symbol ${symbol}) 时失败:`, apiError);
        if (axios.isAxiosError(apiError)) {
           console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
        }
        return null; // Indicate failure
      }
      // --- End wrap ---
    } catch (error) {
      console.error(`[股票池] 更新池 ${poolId} Symbol ${symbol} 前置检查失败:`, error);
      // Propagate the error if it's from initial checks
      throw error;
    }
  },

  /**
   * 获取池索引数据
   * @param isSystemList 是否系统列表
   * @returns 池索引数据数组，失败返回空数组
   */
  async getPoolIndex(isSystemList: boolean) {
    try {
      const token = getToken();
      if (!token) throw new Error('用户未登录，无法获取池索引数据');

      console.log(`[股票池] 获取${isSystemList ? '系统' : '用户'}池索引数据`);

      // --- Wrap API call ---
      try {
        const response = await httpDispatcher.get('/pool/getpoolindex', {
          params: { is_system: isSystemList },
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data && Array.isArray(response.data)) {
          console.log(`[股票池] 成功获取${isSystemList ? '系统' : '用户'}池索引数据: ${response.data.length} 个分类`);
          return response.data;
        } else {
          console.error(`[股票池] 获取${isSystemList ? '系统' : '用户'}池索引数据失败: 返回数据无效`);
          return [];
        }
      } catch (apiError) {
        console.error(`[股票池] 调用获取池索引API (isSystem=${isSystemList}) 时失败:`, apiError);
        if (axios.isAxiosError(apiError)) {
          console.error(`[股票池] Axios Error Details: Status=${apiError.response?.status}`);
        }
        return []; // Return empty array on API call failure
      }
      // --- End wrap ---
    } catch (error) {
      console.error(`[股票池] 获取${isSystemList ? '系统' : '用户'}池索引数据前置检查失败:`, error);
      return []; // Return empty array on error
    }
  }
};

/**
 * 同步指定分类下的池子结构到后端
 * @param categoryId 分类ID
 * @param poolsArray [{ pool_id, pool_name }]
 */
export async function syncCategoryPoolsToBackend(categoryId: number, poolsArray: Array<{ pool_id: number, pool_name: string }>) {
  if (!categoryId) return;
  try {
    await poolDispatcher.updateCategoryPools(categoryId, poolsArray);
    console.log(`[股票池] 分类${categoryId}的池子结构已同步到后端`);
  } catch (e) {
    console.error(`[股票池] 同步分类${categoryId}池子结构到后端失败:`, e);
  }
}

export default poolDispatcher;
[tool.poetry]
name = "jqktrader"
version = "0.1.4"
description = ""
authors = ["pluto <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.8, <3.11"
easyutils = "^0.1.7"
pandas = "^1.5.1"
pytesseract = "^0.3.10"
pypiwin32 = "^223"
pywinauto = "^0.6.8"


[tool.poetry.group.dev.dependencies]
ipykernel = "^6.17.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

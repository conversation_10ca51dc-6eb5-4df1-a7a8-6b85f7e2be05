import React, { useState, useEffect, useRef } from 'react';
import { Input, Button, Radio, Space, AutoComplete, Spin, Dropdown, Divider, Tooltip, MenuProps } from 'antd';
import { 
  SearchOutlined, 
  LoadingOutlined, 
  DownOutlined, 
  WechatOutlined, 
  Line<PERSON><PERSON>Outlined, 
  HistoryOutlined,
  UnorderedListOutlined,
  EditOutlined,
  SelectOutlined
} from '@ant-design/icons';
import Icon from '@ant-design/icons';
import { LuPenLine, LuSlash } from "react-icons/lu";
import { PiLineSegment, PiLineVertical, PiArrowLineRight, PiArrowsHorizontal, PiArrowsVertical, PiRectangle, PiSelectionPlusDuotone } from "react-icons/pi";
import { TbRuler2, Tb<PERSON>hart<PERSON>andle } from "react-icons/tb";
import { LiaGripLinesVerticalSolid } from "react-icons/lia";
import { BsSlashLg } from "react-icons/bs";
import { MdFormatLineSpacing } from "react-icons/md";
import { TfiLayoutLineSolid } from "react-icons/tfi";
import { use<PERSON>tom, useAtomValue } from 'jotai';
import { kline<PERSON><PERSON>, selectedSymbol<PERSON>tom, selectedPeriodAtom, isLoadingAtom, chatVisibleAtom, drawingToolAtom, activeToolbarButtonAtom } from '../../../../store/state';
import { geneDialogVisibleAtom } from '../../models/geneState';
import { marketService } from '@/_Services/marketService';
import marketDispatcher from '@/_Dispatchers/marketDispatcher';
import { Symbol as MarketSymbol, MarketType, KLineInterval } from '@/shared_types/market';
import { toolPaneContentAtom, toolPaneVisibleAtom } from '@/store/state';
import { DrawingToolType } from '@/shared_types/chart';

import GeneDialog from '../GeneDialog';
import './index.less';
import { EventBus } from '@/events/eventBus';
import { ChartEvents, MarketEvents, ChatEvents, KeyboardEvents } from '@/events/events';
import { ToolbarButtonType } from '@/shared_types/ui';
import { CgSearchFound } from 'react-icons/cg';
import { FaGooglePlay, FaPlay, FaPause, FaStop } from 'react-icons/fa';
import { IoIosList } from 'react-icons/io';
import { GoGear } from 'react-icons/go';

const { Search } = Input;

// 全局变量，用于在组件间共享步长设置
let globalStepInterval = 10;

// 根据代码判断市场类型
const getMarketType = (code: string): MarketType => {
  if (code.includes('.SH') || code.includes('.SZ')) {
    return MarketType.INDEX;
  }
  // 可以添加更多的判断逻辑
  return MarketType.STOCK;
};

// 添加设备检测函数
const isMobileDevice = () => {
  return window.innerWidth <= 768;
};

// 自定义SVG图标定义
const LineSvg = () => (
  <img src="icons/line.svg" width="1em" height="1em" style={{ display: 'inline-block' }} />
);

const LineSegmentSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M6 18L18 6"></path>
  </svg>
);

const RaySvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M4 19L20 5"></path>
    <polyline points="16 5 20 5 20 9"></polyline>
  </svg>
);

const TrendLineSvg = () => (
  <img src="icons/slash.svg" width="1em" height="1em" style={{ display: 'inline-block' }} />
);

const HorizontalLineSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M2 12h20"></path>
    <path d="M4 8l-2 4 2 4"></path>
    <path d="M20 8l2 4-2 4"></path>
  </svg>
);

const VerticalLineSvg = () => (
  <img src="icons/minus-vertical.svg" width="1em" height="1em" style={{ display: 'inline-block' }} />
);

const MeasurementSvg = () => (
  <img src="icons/ruler-measure-2.svg" width="1em" height="1em" style={{ display: 'inline-block' }} />
);

const FibonacciSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M2 12h20"></path>
    <path d="M2 6h20"></path>
    <path d="M2 18h20"></path>
  </svg>
);

const RectangleSvg = () => (
  <img src="icons/rectangle.svg" width="1em" height="1em" style={{ display: 'inline-block' }} />
);

const PriceChannelSvg = () => (
  <img src="icons/slashes.svg" width="1em" height="1em" style={{ display: 'inline-block' }} />
);

const TextAnnotationSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M17 6.1H3"></path>
    <path d="M21 12.1H3"></path>
    <path d="M15.1 18H3"></path>
  </svg>
);

// 创建Icon组件，添加类型定义
const LineIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={LineSvg} {...props} />;
const LineSegmentIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={LineSegmentSvg} {...props} />;
const RayIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={RaySvg} {...props} />;
const TrendLineIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={TrendLineSvg} {...props} />;
const HorizontalLineIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={HorizontalLineSvg} {...props} />;
const VerticalLineIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={VerticalLineSvg} {...props} />;
const MeasurementIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={MeasurementSvg} {...props} />;
const PriceChannelIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={PriceChannelSvg} {...props} />;
const FibonacciIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={FibonacciSvg} {...props} />;
const TextAnnotationIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={TextAnnotationSvg} {...props} />;
const RectangleIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={RectangleSvg} {...props} />;
// 创建TradingView风格的垂直线段SVG图标
const VerticalSegmentSvg = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
    {/* 垂直线段 - 使用灰色，更细的线条 */}
    <line x1="12" y1="4" x2="12" y2="20" stroke="#666" strokeWidth="1"/>
    {/* 上端点空心圆圈 - 更小的圆圈 */}
    <circle cx="12" cy="4" r="1.5" fill="none" stroke="#666" strokeWidth="1"/>
    {/* 下端点空心圆圈 - 更小的圆圈 */}
    <circle cx="12" cy="20" r="1.5" fill="none" stroke="#666" strokeWidth="1"/>
  </svg>
);

const VerticalSegmentIcon = (props: React.ComponentProps<typeof Icon>) => <Icon component={VerticalSegmentSvg} {...props} />;

// 更新 DrawingToolIcons 映射表
const DrawingToolIcons: Record<DrawingToolType, React.ReactNode> = {
  [DrawingToolType.NONE]: <LuPenLine size={16} />,
  [DrawingToolType.SEGMENT]: <PiLineSegment size={16} />,
  [DrawingToolType.RAY]: <PiArrowLineRight size={16} />,
  [DrawingToolType.TREND_LINE]: <LuSlash size={16} />,
  [DrawingToolType.HORIZONTAL]: <TfiLayoutLineSolid size={16} />,
  [DrawingToolType.VERTICAL]: <PiLineVertical size={16} />,
  [DrawingToolType.FIBONACCI]: <MdFormatLineSpacing size={16} />,
  [DrawingToolType.RECTANGLE]: <PiRectangle size={16} />,
  [DrawingToolType.MEASUREMENT]: <TbRuler2 size={16} />,
  [DrawingToolType.PRICE_CHANNEL]: <LiaGripLinesVerticalSolid size={16} />,
  [DrawingToolType.VERTICAL_SEGMENT]: <VerticalSegmentIcon />,
  [DrawingToolType.HORIZONTAL_SEGMENT]: <PiArrowsHorizontal size={16} />
};

// 添加画线工具到 KLineChart overlay 的映射
const drawingToolToOverlayMap: Record<DrawingToolType, string> = {
  [DrawingToolType.SEGMENT]: 'segment',
  [DrawingToolType.RAY]: 'rayLine',
  [DrawingToolType.TREND_LINE]: 'straightLine',
  [DrawingToolType.HORIZONTAL]: 'horizontalStraightLine',
  [DrawingToolType.VERTICAL]: 'verticalStraightLine',
  [DrawingToolType.FIBONACCI]: 'fibonacciLine',
  [DrawingToolType.RECTANGLE]: 'rectangle',  // 这个可能需要自定义实现
  [DrawingToolType.MEASUREMENT]: 'measure',  // 这个需要自定义实现
  [DrawingToolType.VERTICAL_SEGMENT]: 'verticalSegment',
  [DrawingToolType.HORIZONTAL_SEGMENT]: 'horizontalSegment',
  [DrawingToolType.PRICE_CHANNEL]: 'priceChannelLine',
  [DrawingToolType.NONE]: ''
};


const ToolBar: React.FC = () => {
  const [searchResults, setSearchResults] = useState<MarketSymbol[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  const [selectedSymbol, setSelectedSymbol] = useAtom(selectedSymbolAtom);
  const [selectedPeriod, setSelectedPeriod] = useAtom(selectedPeriodAtom);
  const [, setGeneDialogVisible] = useAtom(geneDialogVisibleAtom);
  const [previousPeriod, setPreviousPeriod] = useState<KLineInterval>(selectedPeriod);
  const [chatVisible, setChatVisible] = useAtom(chatVisibleAtom);
  const [signalListVisible, setSignalListVisible] = useState(false);
  const [browseHistoryOpen, setBrowseHistoryOpen] = useState(false);
  const [signalScanVisible, setSignalScanVisible] = useState(false);
  // 移除回放状态管理，现在由悬浮窗处理
  // const [isPlaybackMode, setIsPlaybackMode] = useState(false);
  // const [isPlaying, setIsPlaying] = useState(false);
  // const [stepInterval, setStepInterval] = useState(10); // 默认10秒
  // const stepOptions = [1, 2, 3, 4, 5, 10, 15, 20];

  const [isLoading, setLoading] = useAtom(isLoadingAtom);

  const [useDropdown, setUseDropdown] = useState(isMobileDevice());
  const [useToolDropdown, setUseToolDropdown] = useState(isMobileDevice());
  const periodSelectorRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const kline = useAtomValue(klineAtom);

  const [toolPaneContent, setToolPaneContent] = useAtom(toolPaneContentAtom);
  const [toolPaneVisible, setToolPaneVisible] = useAtom(toolPaneVisibleAtom);

  // 使用全局绘图工具状态替换本地状态
  const [currentDrawingTool, setCurrentDrawingTool] = useAtom(drawingToolAtom);

  // 添加工具栏按钮状态
  const [activeToolbarButton, setActiveToolbarButton] = useAtom(activeToolbarButtonAtom);

  // 监听画线完成事件
  useEffect(() => {
    const drawingCompletedSubscription = EventBus.on(
      ChartEvents.Types.DRAWING_COMPLETED,
      (payload: ChartEvents.DrawingCompletedPayload) => {
        console.log('[ToolBar] 收到画线完成事件:', payload);
        // 取消画线按钮的选中状态
        setActiveToolbarButton(ToolbarButtonType.NONE);
        setCurrentDrawingTool(DrawingToolType.NONE);
      }
    );

    return () => {
      drawingCompletedSubscription.unsubscribe();
    };
  }, [setActiveToolbarButton, setCurrentDrawingTool]);

  // 新增：监听 KLINE_READY 事件
  useEffect(() => {
    const subscription = EventBus.on(
      MarketEvents.Types.KLINES_READY,
      (payload: MarketEvents.KLinesReady) => {
        // 更新周期选择按钮状态
        if (payload.kline?.period) {
          setSelectedPeriod(payload.kline.period);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // 监听交易品种和周期变化
  useEffect(() => {
    console.log("检测到所选择的品种或者周期变化：", selectedSymbol, " ", selectedPeriod);

    // 仅首次加载默认品种或用户手动切换品种时执行
    if (!selectedSymbol || !selectedPeriod) return;

    setLoading(true);

    // 通过事件总线请求K线数据
    console.log("[ChartPanel] 使用选择的品种和周期请求K线数据:", selectedSymbol.code, selectedPeriod);
    EventBus.emit(MarketEvents.Types.GET_KLINES, {
      symbol: selectedSymbol,
      interval: getCurrentPeriod(selectedPeriod)
    });

    // 监听K线数据错误事件
    const errorSubscription = EventBus.on(
      MarketEvents.Types.KLINES_ERROR,
      (payload: MarketEvents.KLinesError) => {
        console.error('[ChartPanel] K线数据请求失败:', payload.error);

        setLoading(false);

      }
    );

    // 在K线数据就绪时也清除错误信息
    const klineReadySubscription = EventBus.on(
      MarketEvents.Types.KLINES_READY,
      () => {
        setLoading(false);
      }
    );

    // 当组件卸载时清理订阅
    return () => {
      errorSubscription.unsubscribe();
      klineReadySubscription.unsubscribe();
    };
  }, [selectedSymbol, selectedPeriod]);

  // 获取当前周期的转换函数
  const getCurrentPeriod = (period: string): KLineInterval => {
    const periodMap: Record<string, KLineInterval> = {
      '1m': KLineInterval.MIN1,
      '5m': KLineInterval.MIN5,
      '15m': KLineInterval.MIN15,
      '30m': KLineInterval.MIN30,
      '1h': KLineInterval.HOUR1,
      '1D': KLineInterval.DAY1,
      '1W': KLineInterval.WEEK1,
    };
    return periodMap[period];
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      const isMobile = isMobileDevice();
      setUseDropdown(isMobile);
      setUseToolDropdown(isMobile);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 订阅搜索结果事件
  useEffect(() => {
    const unsubscribe = EventBus.on(MarketEvents.Types.SEARCH_RESULT, (payload: MarketEvents.SearchResult) => {
      setSearchResults(payload.data);
      setShowResults(payload.data.length > 0);
    });

    return () => unsubscribe.unsubscribe();
  }, []);

  // 监听 K 线数据错误事件
  useEffect(() => {
    const subscription = EventBus.on(
      MarketEvents.Types.KLINES_ERROR,
      (payload: MarketEvents.KLinesError) => {
        console.log('[ToolBar] K线数据获取失败，回退到之前的周期:', previousPeriod);
        setSelectedPeriod(previousPeriod);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [previousPeriod, setSelectedPeriod]);

  const [options, setOptions] = useState<{ value: string; label: string; market: MarketType; exchange: string }[]>([]);

  const handleSearch = async (value: string) => {
    if (!value) {
      console.log('handleSearch value', value);
      setOptions([]);
      return;
    }

    const symbols = await marketService.getSymbols(value);
    console.log('handleSearch symbols', symbols);
    setOptions(symbols.map(symbol => ({
      value: symbol.code,
      label: `${symbol.code} - ${symbol.name}`,
      market: symbol.market,
      exchange: symbol.exchange
    })));
  };

  // 选择交易品种
  const handleSelect = (symbol: MarketSymbol) => {
    setSelectedSymbol(symbol);
    setShowResults(false);
    setSearchValue('');  // 清空搜索框
  };

  // 打开基因配置对话框
  const handleGeneConfigClick = () => {
    setGeneDialogVisible(true);
  };

  // 周期选项
  const periodOptions = [
    { label: '1m', value: '1m' },
    { label: '5m', value: '5m' },
    { label: '15m', value: '15m' },
    { label: '30m', value: '30m' },
    { label: '1h', value: '1h' },
    { label: '1D', value: '1D' },
    { label: '1W', value: '1W' },
  ];

  // 处理周期切换
  const handlePeriodChange = (period: KLineInterval) => {
    // 保存当前周期作为回退选项
    setPreviousPeriod(selectedPeriod);
    // 设置新的周期
    setSelectedPeriod(period);
  };

  // 添加处理交易对按钮点击的函数
  const handleSymbolClick = () => {
    // 发布品种按钮点击事件
    console.log('[ToolBar] 品种按钮被点击，发布事件');
    EventBus.emit(KeyboardEvents.Types.SYMBOL_BUTTON_CLICKED, undefined);
  };

  // 处理聊天按钮点击
  const handleChatClick = () => {
    setChatVisible(!chatVisible);
    EventBus.emit(ChatEvents.Types.TOGGLE_CHAT_WINDOW, !chatVisible);
  };

  // 处理信号列表按钮点击
  const handleSignalListClick = () => {
    if (toolPaneContent === 'signal-list' && toolPaneVisible) {
      setToolPaneVisible(false);
      setToolPaneContent(null);
      // 发出信号 SIGNAL_MARKERS_VISIBLE
      EventBus.emit(MarketEvents.Types.SIGNAL_MARKERS_VISIBLE, { signals: [], visible: false });
    } else {
      // 清除其他工具的状态
      setActiveToolbarButton(ToolbarButtonType.NONE);
      setCurrentDrawingTool(DrawingToolType.NONE);
      setToolPaneVisible(true);
      setToolPaneContent('signal-list');
      // 发出信号 SIGNAL_MARKERS_VISIBLE      
    }
  };

  // 处理浏览历史按钮点击
  const handleHistoryClick = () => {
    if (toolPaneContent === 'symbol-list' && toolPaneVisible) {
      setToolPaneVisible(false);
      setToolPaneContent(null);
    } else {
      // 清除其他工具的状态
      setActiveToolbarButton(ToolbarButtonType.NONE);
      setCurrentDrawingTool(DrawingToolType.NONE);
      setToolPaneVisible(true);
      setToolPaneContent('symbol-list');
    }
  };

  // 处理信号观察按钮点击
  const handleSignalScanClick = () => {
    // 清除其他工具的状态
    setActiveToolbarButton(ToolbarButtonType.NONE);
    setCurrentDrawingTool(DrawingToolType.NONE);
    setToolPaneVisible(false);
    setToolPaneContent(null);
    // 打开基因配置对话框
    setGeneDialogVisible(true);
  };

  // 处理画线工具按钮点击
  const handleDrawingToolClick = () => {
    // 如果当前已激活画线工具，则取消激活
    if (activeToolbarButton === ToolbarButtonType.DRAWING) {
      setActiveToolbarButton(ToolbarButtonType.NONE);
      setCurrentDrawingTool(DrawingToolType.NONE);
    } else {
      // 清除其他工具的状态
      setToolPaneVisible(false);
      setToolPaneContent(null);
      // 激活画线工具
      setActiveToolbarButton(ToolbarButtonType.DRAWING);
    }
  };
  
  // 修改范围选择处理函数
  const handleRangeSelectClick = () => {
    // 如果当前已激活形态选择，则取消激活
    if (activeToolbarButton === ToolbarButtonType.RANGE_SELECT) {
      console.log('[ToolBar] 取消形态选择');
      setActiveToolbarButton(ToolbarButtonType.NONE);
    } else {
      console.log('[ToolBar] 激活形态选择');
      // 清除其他工具的状态
      setCurrentDrawingTool(DrawingToolType.NONE);
      setToolPaneVisible(false);
      setToolPaneContent(null);
      // 激活形态选择
      setActiveToolbarButton(ToolbarButtonType.RANGE_SELECT);
    }
  };
  
  // 选择当前显示的画线工具图标
  const getDrawingToolIcon = (tool: DrawingToolType) => {
    const isActive = activeToolbarButton === ToolbarButtonType.DRAWING || currentDrawingTool !== DrawingToolType.NONE;
    return tool === DrawingToolType.NONE ? 
      <LuPenLine size={16} style={{ color: isActive ? 'white' : 'inherit' }} /> : 
      React.cloneElement(DrawingToolIcons[tool] as React.ReactElement, {
        style: { color: isActive ? 'white' : 'inherit' }
      });
  };

  // 修改画线工具菜单项
  const drawingToolMenuItems: MenuProps['items'] = [
    {
      key: DrawingToolType.SEGMENT,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.SEGMENT ? 'var(--primary-color)' : 'inherit'
        }}>线段</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.SEGMENT ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.SEGMENT]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.SEGMENT) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          // 清除其他工具的状态
          setToolPaneVisible(false);
          setToolPaneContent(null);
          // 激活画线工具
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.SEGMENT);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.SEGMENT]
          });
        }
      },
    },
    {
      key: DrawingToolType.VERTICAL_SEGMENT,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.VERTICAL_SEGMENT ? 'var(--primary-color)' : 'inherit'
        }}>垂直线段</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.VERTICAL_SEGMENT ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.VERTICAL_SEGMENT]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.VERTICAL_SEGMENT) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.VERTICAL_SEGMENT);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.VERTICAL_SEGMENT]
          });
        }
      },
    },
    {
      key: DrawingToolType.HORIZONTAL_SEGMENT,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.HORIZONTAL_SEGMENT ? 'var(--primary-color)' : 'inherit'
        }}>水平线段</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.HORIZONTAL_SEGMENT ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.HORIZONTAL_SEGMENT]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.HORIZONTAL_SEGMENT) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.HORIZONTAL_SEGMENT);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.HORIZONTAL_SEGMENT]
          });
        }
      },
    },
    {
      key: DrawingToolType.RAY,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.RAY ? 'var(--primary-color)' : 'inherit'
        }}>射线</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.RAY ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.RAY]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.RAY) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.RAY);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.RAY]
          });
        }
      },
    },
    {
      key: DrawingToolType.TREND_LINE,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.TREND_LINE ? 'var(--primary-color)' : 'inherit'
        }}>趋势线</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.TREND_LINE ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.TREND_LINE]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.TREND_LINE) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.TREND_LINE);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.TREND_LINE]
          });
        }
      },
    },
    {
      key: DrawingToolType.HORIZONTAL,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.HORIZONTAL ? 'var(--primary-color)' : 'inherit'
        }}>水平线</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.HORIZONTAL ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.HORIZONTAL]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.HORIZONTAL) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.HORIZONTAL);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.HORIZONTAL]
          });
        }
      },
    },
    {
      key: DrawingToolType.VERTICAL,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.VERTICAL ? 'var(--primary-color)' : 'inherit'
        }}>垂直线</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.VERTICAL ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.VERTICAL]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.VERTICAL) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.VERTICAL);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.VERTICAL]
          });
        }
      },
    },
    {
      key: DrawingToolType.FIBONACCI,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.FIBONACCI ? 'var(--primary-color)' : 'inherit'
        }}>斐波那契</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.FIBONACCI ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.FIBONACCI]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.FIBONACCI) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.FIBONACCI);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.FIBONACCI]
          });
        }
      },
    },
    {
      key: DrawingToolType.RECTANGLE,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.RECTANGLE ? 'var(--primary-color)' : 'inherit'
        }}>矩形</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.RECTANGLE ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.RECTANGLE]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.RECTANGLE) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.RECTANGLE);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.RECTANGLE]
          });
        }
      },
    },
    {
      key: DrawingToolType.PRICE_CHANNEL,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.PRICE_CHANNEL ? 'var(--primary-color)' : 'inherit'
        }}>价格通道</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.PRICE_CHANNEL ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.PRICE_CHANNEL]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.PRICE_CHANNEL) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.PRICE_CHANNEL);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.PRICE_CHANNEL]
          });
        }
      },
    },
    {
      key: DrawingToolType.MEASUREMENT,
      label: (
        <span style={{
          color: currentDrawingTool === DrawingToolType.MEASUREMENT ? 'var(--primary-color)' : 'inherit'
        }}>测量线</span>
      ),
      icon: <span style={{
        color: currentDrawingTool === DrawingToolType.MEASUREMENT ? 'var(--primary-color)' : 'inherit'
      }}>{DrawingToolIcons[DrawingToolType.MEASUREMENT]}</span>,
      onClick: () => {
        if (currentDrawingTool === DrawingToolType.MEASUREMENT) {
          setActiveToolbarButton(ToolbarButtonType.NONE);
          setCurrentDrawingTool(DrawingToolType.NONE);
        } else {
          setActiveToolbarButton(ToolbarButtonType.DRAWING);
          setCurrentDrawingTool(DrawingToolType.MEASUREMENT);
          EventBus.emit(ChartEvents.Types.DRAWING_TOOL_SELECTED, {
            overlayName: drawingToolToOverlayMap[DrawingToolType.MEASUREMENT]
          });
        }
      },
    }
  ];

  // 恢复修改工具按钮菜单项，添加选中状态
  const toolMenuItems: MenuProps['items'] = [
    {
      key: 'signal-list',
      label: (
        <span style={{ 
          color: toolPaneContent === 'signal-list' && toolPaneVisible ? 'var(--primary-color)' : 'inherit' 
        }}>
          信号列表
        </span>
      ),
      icon: <LineChartOutlined style={{ 
        color: toolPaneContent === 'signal-list' && toolPaneVisible ? 'var(--primary-color)' : 'inherit' 
      }} />,
      onClick: handleSignalListClick,
    },
    {
      key: 'range-select',
      label: (
        <span style={{ 
          color: activeToolbarButton === ToolbarButtonType.RANGE_SELECT ? 'var(--primary-color)' : 'inherit' 
        }}>
          形态选择
        </span>
      ),
      icon: <PiSelectionPlusDuotone style={{ 
        color: activeToolbarButton === ToolbarButtonType.RANGE_SELECT ? 'var(--primary-color)' : 'inherit' 
      }} />,
      onClick: handleRangeSelectClick,
    },
    /*({
      key: 'chat',
      label: (
        <span style={{ 
          color: chatVisible ? 'var(--primary-color)' : 'inherit' 
        }}>
          聊天窗口
        </span>
      ),
      icon: <WechatOutlined style={{ 
        color: chatVisible ? 'var(--primary-color)' : 'inherit' 
      }} />,
      onClick: handleChatClick,
    },*/
    {
      key: 'signal-scan',
      label: (
        <span style={{ 
          color: false ? 'var(--primary-color)' : 'inherit' 
        }}>
          信号观察
        </span>
      ),
      icon: <CgSearchFound size={16} />,
      onClick: handleSignalScanClick,
    },
    {
      key: 'drawing',
      label: (
        <span style={{ 
          color: activeToolbarButton === ToolbarButtonType.DRAWING ? 'var(--primary-color)' : 'inherit' 
        }}>
          画线工具
        </span>
      ),
      icon: <span style={{ 
        color: activeToolbarButton === ToolbarButtonType.DRAWING ? 'var(--primary-color)' : 'inherit',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        {getDrawingToolIcon(currentDrawingTool)}
      </span>,
      // 在移动端时，展示子菜单而不是激活画线工具
      children: useToolDropdown ? drawingToolMenuItems : undefined,
      onClick: useToolDropdown ? undefined : handleDrawingToolClick,
    },
    {
      key: 'symbol-list',
      label: (
        <span style={{ 
          color: toolPaneContent === 'symbol-list' && toolPaneVisible ? 'var(--primary-color)' : 'inherit' 
        }}>
          品种列表
        </span>
      ),
      icon: <HistoryOutlined style={{ 
        color: toolPaneContent === 'symbol-list' && toolPaneVisible ? 'var(--primary-color)' : 'inherit' 
      }} />,
      onClick: handleHistoryClick,
    },
  ];

  // 历史回放按钮相关渲染
  const renderPlaybackControls = () => {
    // 只显示进入回放模式的按钮，回放控制由悬浮窗处理
    return (
      <Tooltip title="历史回放">
        <Button
          shape="circle"
          icon={<FaPlay />}
          onClick={() => {
            EventBus.emit(ChartEvents.Types.ENTER_PLAYBACK_MODE, {});
          }}
          type="default"
        />
      </Tooltip>
    );
  }

  // 修改画线工具状态变化监听，处理工具状态变更
  useEffect(() => {
    // 如果取消选择画线工具，更新按钮状态
    if (currentDrawingTool === DrawingToolType.NONE && activeToolbarButton === ToolbarButtonType.DRAWING) {
      setActiveToolbarButton(ToolbarButtonType.NONE);
    }
    // 如果选择了画线工具，确保按钮状态为绘图模式
    else if (currentDrawingTool !== DrawingToolType.NONE && activeToolbarButton !== ToolbarButtonType.DRAWING) {
      setActiveToolbarButton(ToolbarButtonType.DRAWING);
    }
  }, [currentDrawingTool, activeToolbarButton]);

  // 同步全局变量
  // useEffect(() => { globalStepInterval = stepInterval; }, [stepInterval]);

  return (
    <>
      <div className="toolbar" style={{ 
        padding: '0',
        borderBottom: '1px solid var(--border-color)',
        background: 'var(--background-color)',
        height: '38px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: isMobileDevice() ? 'flex-start' : 'space-between' // 手机端使用flex-start，桌面端保持space-between
      }}>
        <Space size={isMobileDevice() ? 4 : 0} align="center" style={{ height: '100%' }}>
          {/* 交易对按钮，需要有左间隔 */}
          <Button 
            type="text"
            onClick={handleSymbolClick}
            style={{
              marginLeft: isMobileDevice() ? '8px' : '15px', // 手机屏幕下减少左边距
              borderRadius: '3px',
              border: '1px solid var(--border-color)',
              backgroundColor: 'var(--component-background, var(--background-color-secondary))',
              fontWeight: 500,
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.03)', // 添加轻微阴影增强可见性
            }}
            className="symbol-button"
            // disabled={isPlaybackMode}
          >
            {kline.symbol ? (
              kline.symbol.code === kline.symbol.name ? 
                kline.symbol.code : 
                `${kline.symbol.code} - ${kline.symbol.name}`
            ) : 'ORDIUSDT'}
          </Button>

          <Divider type="vertical" />

          {/* 周期选择器 */}
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            height: '100%',
            marginRight: isMobileDevice() ? '8px' : '0' // 手机端减少右边距
          }}>
            {useDropdown ? (
              <Dropdown
                menu={{
                  items: periodOptions.map(option => ({
                    key: option.value,
                    label: option.label,
                    onClick: () => handlePeriodChange(option.value as KLineInterval)
                  })),
                }}
                trigger={['click']}
              >
                <Button>
                  {periodOptions.find(option => option.value === selectedPeriod)?.label || selectedPeriod}
                  <DownOutlined style={{ marginLeft: 8, opacity: 0.65 }} />
                </Button>
              </Dropdown>
            ) : (
              <Radio.Group
                value={selectedPeriod}
                onChange={e => handlePeriodChange(e.target.value as KLineInterval)}
                optionType="button"
                buttonStyle="solid"
                className="period-selector"
                options={periodOptions}
                // disabled={isPlaybackMode}
              />
            )}
          </div>

          {/* Loading 指示器 - 移动到此处 */}
          {isLoading && (
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              height: '100%',
              marginLeft: isMobileDevice() ? '4px' : '8px' // 手机端减少左边距
            }}>
              <Spin indicator={<LoadingOutlined style={{ fontSize: 16, color: 'var(--text-color)' }} spin />} />
            </div>
          )}
        </Space>

        {/* 右侧工具按钮 */}
        <Space size={8} style={{ 
          marginRight: isMobileDevice() ? '8px' : '15px',
          marginLeft: isMobileDevice() ? 'auto' : '0' // 手机端使用auto让右侧按钮推到最右边
        }}>
          {renderPlaybackControls()}
          {/* 工具按钮 - 大屏幕显示按钮，小屏幕显示下拉菜单 */}
          {!useToolDropdown ? (
            <Space className="tool-buttons" size={isMobileDevice() ? 4 : 8}>
              <Tooltip title="信号观察">
                <Button
                  shape="circle"
                  icon={<FaGooglePlay size={16} />}
                  onClick={handleSignalScanClick}
                  type="default"
                  // disabled={isPlaybackMode}
                />
              </Tooltip>
              <Tooltip title="信号列表">
                <Button
                  shape="circle"
                  icon={<IoIosList size={16} />}
                  onClick={handleSignalListClick}
                  type={toolPaneContent === 'signal-list' && toolPaneVisible ? "primary" : "default"}
                  // disabled={isPlaybackMode}
                />
              </Tooltip>
              <Tooltip title="形态选择">
                <Button
                  shape="circle"
                  icon={<PiSelectionPlusDuotone />}
                  onClick={handleRangeSelectClick}
                  type={activeToolbarButton === ToolbarButtonType.RANGE_SELECT ? "primary" : "default"}
                  // disabled={isPlaybackMode}
                />
              </Tooltip>
              <Tooltip title="画线工具">
                <Dropdown menu={{ items: drawingToolMenuItems }} trigger={['click']} disabled={false}>
                  <Button
                    shape="circle"
                    icon={<span style={{ 
                      color: activeToolbarButton === ToolbarButtonType.DRAWING || currentDrawingTool !== DrawingToolType.NONE ? 'white' : 'inherit',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      {getDrawingToolIcon(currentDrawingTool)}
                    </span>}
                    type={(activeToolbarButton === ToolbarButtonType.DRAWING || currentDrawingTool !== DrawingToolType.NONE) ? "primary" : "default"}
                    disabled={false}
                  />
                </Dropdown>
              </Tooltip>
              <Tooltip title="品种列表">
                <Button
                  shape="circle"
                  icon={<HistoryOutlined />}
                  onClick={handleHistoryClick}
                  type={toolPaneContent === 'symbol-list' && toolPaneVisible ? "primary" : "default"}
                  // disabled={isPlaybackMode}
                />
              </Tooltip>
            </Space>
          ) : (
            // 小屏幕时显示工具下拉菜单
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'signal-scan',
                    label: '信号观察',
                    icon: <FaGooglePlay size={16} />,
                    onClick: handleSignalScanClick,
                  },
                  {
                    key: 'signal-list',
                    label: '信号列表',
                    icon: <IoIosList size={16} />,
                    onClick: handleSignalListClick,
                  },
                  {
                    key: 'range-select',
                    label: '形态选择',
                    icon: <PiSelectionPlusDuotone />,
                    onClick: handleRangeSelectClick,
                  },
                  {
                    key: 'drawing-tools',
                    label: '画线工具',
                    icon: <LuPenLine size={16} />,
                    children: drawingToolMenuItems,
                  },
                  {
                    key: 'symbol-list',
                    label: '品种列表',
                    icon: <HistoryOutlined />,
                    onClick: handleHistoryClick,
                  },
                ]
              }}
              trigger={['click']}
            >
              <Button shape="circle" icon={<GoGear />} />
            </Dropdown>
          )}
        </Space>

        {/* 隐藏现有的 AutoComplete */}
        <div style={{ display: 'none' }}>
          <AutoComplete
            placeholder="搜索交易品种"
            dropdownStyle={{ width: '200px' }}
            onSearch={handleSearch}
            options={options}
            allowClear={true}
            onSelect={(value) => {
              setSearchValue(value);
              const symbol = options.find(opt => opt.value === value);
              if (symbol) {
                setSelectedSymbol({
                  code: value,
                  name: symbol.label.split(' - ')[1],
                  market: symbol.market || getMarketType(value),
                  exchange: symbol.exchange
                } as MarketSymbol);
              }
              (document.activeElement as HTMLElement).blur();
            }}
            style={{ minWidth: 120, maxWidth: 200 }}
          />
        </div>
      </div>
      <GeneDialog />
    </>
  );
};

// 导出getter
export function getCurrentStepInterval() {
  return globalStepInterval;
}

export default ToolBar;
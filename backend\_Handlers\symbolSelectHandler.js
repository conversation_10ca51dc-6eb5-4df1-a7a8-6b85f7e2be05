/**
 * 选股处理器
 * 负责处理选股请求、启动选股引擎、监听Redis进度通知
 */

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { EventEmitter } = require('events');
const redis = require('redis');
const config = require('../config.json');
const { v4: uuidv4 } = require('uuid');
const { spawn } = require('child_process');
const path = require('path'); 

// 创建简单的日志记录器
const logger = {
    info: (message) => console.log(`[SymbolSelectHandler INFO] ${message}`),
    warn: (message) => console.warn(`[SymbolSelectHandler WARN] ${message}`),
    error: (message) => console.error(`[SymbolSelectHandler ERROR] ${message}`),
    debug: (message) => console.log(`[SymbolSelectHandler DEBUG] ${message}`)
};

// 存储选股任务状态
const stockSelectionTasks = new Map(); // taskId -> { status, progress, result }

// 全局Socket.IO命名空间引用
let stockProgressNamespace = null;

/**
 * 设置Socket.IO命名空间引用
 * @param {SocketIO.Namespace} namespace 选股进度命名空间
 */
function setSocketNamespace(namespace) {
    if (!namespace) {
        logger.warn('尝试设置空的Socket.IO命名空间引用');
        return;
    }
    stockProgressNamespace = namespace;
    logger.info('选股处理器已设置Socket.IO命名空间引用');
}

// Redis客户端
let redisClient = null;
let redisSubscriber = null;

// 初始化Redis连接
function initRedis() {
    try {
        // 用于发布的客户端
        redisClient = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        redisClient.on('error', (err) => {
            logger.error(`Redis Publisher Error: ${err.message}`);
        });

        // 用于订阅的客户端
        redisSubscriber = redis.createClient({
            socket: {
                host: config.redis.host,
                port: config.redis.port
            },
            password: config.redis.password,
            database: config.redis.db
        });
        redisSubscriber.on('error', (err) => {
            logger.error(`Redis Subscriber Error: ${err.message}`);
        });

        return true;
    } catch (error) {
        logger.error(`Redis初始化失败: ${error.message}`);
        return false;
    }
}

class SymbolSelectHandler extends EventEmitter {
    constructor() {
        super();
        this.router = express.Router();
        this.isInitialized = false;
        this.init();
    }

    /**
     * 初始化处理器
     */
    async init() {
        if (this.isInitialized) {
            return;
        }

        try {
            // 初始化Redis连接
            if (!initRedis()) {
                throw new Error('Redis初始化失败');
            }

            // 连接Redis
            await redisClient.connect();
            logger.info('Redis Publisher connected.');
            await redisSubscriber.connect();
            logger.info('Redis Subscriber connected.');

            // 订阅选股进度通知频道
            await redisSubscriber.pSubscribe('stock_selection_progress:*', (message, channel) => {
                this.handleProgressSignal(message, channel);
            });
            logger.info('Successfully subscribed to stock_selection_progress:*');

            this.initRoutes();

            this.isInitialized = true;
            logger.info('SymbolSelectHandler初始化完成');
            // 触发初始化完成事件
            this.emit('initialized');
        } catch (error) {
            logger.error(`SymbolSelectHandler初始化失败: ${error.message}`);
        }
    }

    getRouter() {
        return this.router;
    }


    /**
     * 处理Redis进度信号
     */
    handleProgressSignal(message, channel) {
        try {
            logger.info(`[DEBUG] 收到Redis消息 - 频道: ${channel}, 消息: ${message}`);

            const taskId = channel.split(':')[1];
            const progressData = JSON.parse(message);

            logger.info(`收到选股进度通知: ${taskId} - ${progressData.progress}% - 阶段: ${progressData.stage}`);
            logger.info(`进度详情: 当前${progressData.current}/${progressData.total}, 已选中${progressData.selectedCount}`);

            // 更新任务状态
            this.updateTaskProgress(progressData);

            // 通过Socket.IO推送进度更新
            if (stockProgressNamespace) {
                logger.info(`[DEBUG] 通过Socket.IO推送进度更新到房间: task_${taskId}`);

                // 检查房间中是否有客户端
                const room = stockProgressNamespace.adapter.rooms.get(`task_${taskId}`);
                const clientCount = room ? room.size : 0;
                logger.info(`[DEBUG] 房间 task_${taskId} 中有 ${clientCount} 个客户端`);

                stockProgressNamespace.to(`task_${taskId}`).emit('progress_update', progressData);
                logger.info(`[DEBUG] 已发送progress_update事件到房间 task_${taskId}`);
            } else {
                logger.warn(`Socket.IO命名空间未设置，无法推送进度更新`);
            }

            // 发送进度事件到前端
            this.emit('progress', progressData);

        } catch (error) {
            logger.error(`解析进度消息失败: ${error.message}`);
            logger.error(`原始消息: ${message}`);
            logger.error(`错误堆栈: ${error.stack}`);
        }
    }

    /**
     * 更新任务进度
     */
    updateTaskProgress(progressData) {
        const { taskId } = progressData;
        const task = stockSelectionTasks.get(taskId);
        
        if (task) {
            task.progress = progressData.progress;
            task.current = progressData.current;
            task.total = progressData.total;
            task.selectedCount = progressData.selectedCount;
            task.symbolInfo = progressData.symbolInfo;
            task.stage = progressData.stage;
            
            logger.debug(`更新任务进度: ${taskId} - ${progressData.progress}%`);
        }
    }

    /**
     * 启动选股任务
     */
    async startStockSelection(req, res) {
            try {
                const { candidate_type, strategy_module, strategy_parameters } = req.body;
                
                // 生成任务ID
                const taskId = uuidv4();
                
                // 创建任务记录
                const task = {
                    taskId,
                    status: 'running',
                    progress: 0,
                    current: 0,
                    total: 0,
                    selectedCount: 0,
                    symbolInfo: '',
                    stage: '开始选股',
                    createdAt: new Date(),
                    result: null
                };
                
                stockSelectionTasks.set(taskId, task);
                
                logger.info(`启动选股任务: ${taskId}`);
                
                // 启动Python选股引擎，传递candidate_type
                await this.startPythonSelectionEngine(taskId, candidate_type, strategy_module, strategy_parameters);
                
                res.json({
                    success: true,
                    taskId: taskId,
                    message: '选股任务已启动'
                });
                
            } catch (error) {
                logger.error(`启动选股任务失败: ${error.message}`);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        }


    /**
     * 启动Python选股引擎
     */
    // 异步启动Python选股引擎
    async startPythonSelectionEngine(taskId, candidateType, strategyModule, strategyParameters) {
            try {
                // Python脚本路径
                const scriptPath = path.join(__dirname, '../_Providers/_Python/stock_selection/engine.py');
                
                // 构建配置参数
                const engineConfig = {
                    candidate_type: candidateType,
                    strategy_module: strategyModule,
                    threshold: strategyParameters.threshold,
                    values: strategyParameters.values,
                    task_id: taskId,
                };
    
                console.log('[symbolSelectHandler] 传送给选股引擎的参数:', engineConfig);
                console.log('[symbolSelectHandler] strategyParameters:', strategyParameters);
                console.log('[symbolSelectHandler] values:', strategyParameters.values);
                console.log('[symbolSelectHandler] threshold:', strategyParameters.threshold);
                console.log('[symbolSelectHandler] candidateType:', candidateType);
                
                // 启动Python进程，通过命令行参数传递配置
                const pythonProcess = spawn('python', [scriptPath, JSON.stringify(engineConfig)], {
                    stdio: ['pipe', 'pipe', 'pipe']
                });
                
                // 监听输出
                pythonProcess.stdout.on('data', (data) => {
                    logger.info(`选股引擎输出: ${data.toString()}`);
                });
                
                pythonProcess.stderr.on('data', (data) => {
                    logger.error(`选股引擎错误: ${data.toString()}`);
                });
                
                pythonProcess.on('close', (code) => {
                    logger.info(`选股引擎进程结束，退出码: ${code}`);
                    
                    // 更新任务状态
                    const task = stockSelectionTasks.get(taskId);
                    if (task) {
                        task.status = code === 0 ? 'completed' : 'failed';
                        task.completedAt = new Date();
                    }
                });
                
                // 保存进程引用
                const task = stockSelectionTasks.get(taskId);
                if (task) {
                    task.pythonProcess = pythonProcess;
                }
                
            } catch (error) {
                logger.error(`启动Python选股引擎失败: ${error.message}`);
                throw error;
            }
        }



    /**
     * 获取选股任务状态
     */
    async getTaskStatus(req, res) {
        try {
            const { taskId } = req.params;
            
            const task = stockSelectionTasks.get(taskId);
            if (!task) {
                return res.status(404).json({
                    success: false,
                    error: '任务不存在'
                });
            }
            
            res.json({
                success: true,
                data: {
                    taskId: task.taskId,
                    status: task.status,
                    progress: task.progress,
                    current: task.current,
                    total: task.total,
                    selectedCount: task.selectedCount,
                    symbolInfo: task.symbolInfo,
                    stage: task.stage,
                    createdAt: task.createdAt,
                    completedAt: task.completedAt,
                    result: task.result
                }
            });
            
        } catch (error) {
            logger.error(`获取任务状态失败: ${error.message}`);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * 停止选股任务
     */
    async stopTask(req, res) {
        try {
            const { taskId } = req.params;
            
            const task = stockSelectionTasks.get(taskId);
            if (!task) {
                return res.status(404).json({
                    success: false,
                    error: '任务不存在'
                });
            }
            
            // 停止Python进程
            if (task.pythonProcess) {
                task.pythonProcess.kill('SIGTERM');
                logger.info(`已停止选股任务: ${taskId}`);
            }
            
            // 更新任务状态
            task.status = 'stopped';
            task.completedAt = new Date();
            
            res.json({
                success: true,
                message: '选股任务已停止'
            });
            
        } catch (error) {
            logger.error(`停止任务失败: ${error.message}`);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * 获取所有任务列表
     */
    async getTaskList(req, res) {
        try {
            const tasks = Array.from(stockSelectionTasks.values()).map(task => ({
                taskId: task.taskId,
                status: task.status,
                progress: task.progress,
                selectedCount: task.selectedCount,
                stage: task.stage,
                createdAt: task.createdAt,
                completedAt: task.completedAt
            }));
            
            res.json({
                success: true,
                data: tasks
            });
            
        } catch (error) {
            logger.error(`获取任务列表失败: ${error.message}`);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * 测试选股进度功能
     */
    async testStockSelectionProgress(req, res) {
        try {
            // 生成任务ID
            const taskId = uuidv4();
            
            // 创建测试任务记录
            const task = {
                taskId,
                status: 'running',
                progress: 0,
                current: 0,
                total: 100,
                selectedCount: 0,
                symbolInfo: '测试模式',
                stage: '开始测试',
                createdAt: new Date(),
                result: null
            };
            
            stockSelectionTasks.set(taskId, task);
            
            logger.info(`启动测试选股任务: ${taskId}`);
            
            // 启动测试Python进程
            await this.startTestPythonEngine(taskId);
            
            res.json({
                success: true,
                taskId: taskId,
                message: '测试选股任务已启动'
            });
            
        } catch (error) {
            logger.error(`启动测试选股任务失败: ${error.message}`);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * 启动测试Python引擎
     */
    async startTestPythonEngine(taskId) {
        try {
            // Python脚本路径
            const scriptPath = path.join(__dirname, '../_Providers/_Python/stock_selection/engine.py');
            
            // 构建测试配置参数
            const testConfig = {
                test_mode: true,  // 标记为测试模式
                task_id: taskId,
                candidate_type: 0,  // A股
                strategy_module: 'shape_matching',
                threshold: 0.8,
                values: [1, 2, 3, 4, 5]  // 简化的测试数据
            };
    
            console.log('[symbolSelectHandler] 传送给测试引擎的参数:', testConfig);
            
            // 启动Python进程，通过命令行参数传递配置
            const pythonProcess = spawn('python', [scriptPath, JSON.stringify(testConfig)], {
                stdio: ['pipe', 'pipe', 'pipe']
            });
            
            // 监听输出
            pythonProcess.stdout.on('data', (data) => {
                logger.info(`测试引擎输出: ${data.toString()}`);
            });
            
            pythonProcess.stderr.on('data', (data) => {
                logger.error(`测试引擎错误: ${data.toString()}`);
            });
            
            pythonProcess.on('close', (code) => {
                logger.info(`测试引擎进程结束，退出码: ${code}`);
                
                // 更新任务状态
                const task = stockSelectionTasks.get(taskId);
                if (task) {
                    task.status = code === 0 ? 'completed' : 'failed';
                    task.completedAt = new Date();
                }
            });
            
            // 保存进程引用
            const task = stockSelectionTasks.get(taskId);
            if (task) {
                task.pythonProcess = pythonProcess;
            }
            
        } catch (error) {
            logger.error(`启动测试Python引擎失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 初始化Socket.IO服务
     * @param {SocketIO.Namespace} namespace Socket.IO命名空间
     */
    initSocketIO(namespace) {
        if (!namespace) {
            logger.warn('尝试设置空的Socket.IO命名空间引用');
            return;
        }
        stockProgressNamespace = namespace;
        logger.info('选股处理器已设置Socket.IO命名空间引用');

        // 处理连接事件
        namespace.on('connection', (socket) => {
            logger.info(`选股进度命名空间收到新连接: ${socket.id}`);

            // 处理加入任务房间
            socket.on('join_task', (data) => {
                const { taskId } = data;
                if (taskId) {
                    socket.join(`task_${taskId}`);
                    logger.info(`客户端 ${socket.id} 加入任务房间: task_${taskId}`);
                }
            });

            // 处理离开任务房间
            socket.on('leave_task', (data) => {
                const { taskId } = data;
                if (taskId) {
                    socket.leave(`task_${taskId}`);
                    logger.info(`客户端 ${socket.id} 离开任务房间: task_${taskId}`);
                }
            });

            // 处理断开连接
            socket.on('disconnect', (reason) => {
                logger.info(`选股进度客户端断开连接: ${socket.id}, 原因: ${reason}`);
            });
        });
    }

    /**
     * 设置路由
     */
    initRoutes() {

        this.router.post('/start', authenticateToken, this.startStockSelection.bind(this));
        this.router.post('/test', authenticateToken, this.testStockSelectionProgress.bind(this));
        this.router.get('/status/:taskId', authenticateToken, this.getTaskStatus.bind(this));
        this.router.post('/stop/:taskId', authenticateToken, this.stopTask.bind(this));
        this.router.get('/tasks', authenticateToken, this.getTaskList.bind(this));
    }
}

// 创建处理器实例
const symbolSelectHandler = new SymbolSelectHandler();

module.exports = {
    router,
    symbolSelectHandler,
    setSocketNamespace
};
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 强制使用 UTF-8 编码
import sys
import os
import codecs


import datetime
import json # <-- 添加导入
from flask import Flask, request, jsonify
from pytdx.hq import TdxHq_API
from pytdx.exhq import TdxExHq_API
from pytdx.params import TDXParams
import pandas as pd
import logging
import time # Import time module for timestamp calculation
import psutil # Import psutil for process and connection lookup
import pytz  # <--- 引入 pytz
from typing import Dict, Union, Optional, Any, Set, List # <-- Added for type hinting
import asyncio
import threading
from collections import defaultdict
import socketio
from aiohttp import web
import redis
import json as pyjson
import h5py
import argparse
from filelock import FileLock
import atexit
import traceback

# 导入tick数据处理模块
from tick_data_handler import TickDataHandler

REDIS_CONFIG_FILE = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../config.json')) # Redis 配置文件名


# --- 配置常量 ---
CONFIG_FILE = 'tdx_servers.json' # 配置文件名

DEFAULT_TDX_HQ_IP = '**************' # 默认普通行情 IP (来自用户成功案例)
DEFAULT_TDX_HQ_PORT = 7709       # 默认普通行情端口
DEFAULT_TDX_EXHQ_IP = '*************' # 默认扩展行情 IP (来自用户成功案例)
DEFAULT_TDX_EXHQ_PORT = 7727      # 默认扩展行情端口
# ----------------


os.environ['PYTHONIOENCODING'] = 'utf-8'
try:
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
except Exception as e:
    # 某些环境（如 pm2/Windows 服务）下 sys.stdout 没有 buffer 属性，直接跳过
    pass

def load_redis_config(filename: str) -> dict:
    # 输出当前工作目录，便于调试文件路径问题
    print(f"[TDX 数据服务] 当前工作目录: {os.getcwd()}")
    try:
        loaded_config = load_server_config(CONFIG_FILE)
        HQ_TDX_IP = loaded_config['hq']['ip']
        HQ_TDX_PORT = loaded_config['hq']['port']
        EXHQ_TDX_IP = loaded_config['exhq']['ip']
        EXHQ_TDX_PORT = loaded_config['exhq']['port']
    except Exception as e:
        logger.error(f"[TDX 数据服务] 初始化行情服务器连接参数失败: {e}")
    """只读取 config.json 里的 redis 字段，返回 redis 配置字典。"""
    if not os.path.exists(filename):
        print(f"[TDX 数据服务] 检查到 Redis 配置文件不存在: {filename}")
        raise FileNotFoundError(f"Redis 配置文件不存在: {filename}")
    with open(filename, 'r', encoding='utf-8') as f:
        config = json.load(f)
    redis_cfg = config.get('redis', {})
    if not redis_cfg:
        raise ValueError(f"配置文件 {filename} 中未找到 redis 字段")
    return redis_cfg


# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='[%(asctime)s] [%(levelname)s] [%(module)s] %(message)s',
                   encoding='utf-8')  # 添加 encoding 参数
logger = logging.getLogger(__name__)


# --- 配置文件加载/保存函数 ---
def load_server_config(filename: str) -> Dict[str, Dict[str, Any]]:
    """加载服务器配置，如果文件不存在或无效，则返回默认值"""
    defaults = {
        'hq': {'ip': DEFAULT_TDX_HQ_IP, 'port': DEFAULT_TDX_HQ_PORT},
        'exhq': {'ip': DEFAULT_TDX_EXHQ_IP, 'port': DEFAULT_TDX_EXHQ_PORT}
    }
    if not os.path.exists(filename):
        logger.info(f"[TDX 数据服务 配置] 配置文件 '{filename}' 不存在，使用默认配置。")
        return defaults
    try:
        with open(filename, 'r') as f:
            config = json.load(f)
            # 基本验证，确保至少有 hq 和 exhq 键，且包含 ip 和 port
            if isinstance(config, dict) and \
               'hq' in config and isinstance(config['hq'], dict) and 'ip' in config['hq'] and 'port' in config['hq'] and \
               'exhq' in config and isinstance(config['exhq'], dict) and 'ip' in config['exhq'] and 'port' in config['exhq']:
                logger.info(f"[TDX 数据服务 配置] 从 '{filename}' 加载配置成功。")
                # 确保端口是整数
                config['hq']['port'] = int(config['hq']['port'])
                config['exhq']['port'] = int(config['exhq']['port'])
                return config
            else:
                logger.warning(f"[TDX 数据服务 配置] 配置文件 '{filename}' 格式无效，使用默认配置。")
                return defaults
    except (json.JSONDecodeError, ValueError, Exception) as e:
        logger.error(f"[TDX 数据服务 配置] 读取或解析配置文件 '{filename}' 时出错: {e}，使用默认配置。")
        return defaults

def save_server_config(filename: str, config: Dict[str, Dict[str, Any]]) -> None:
    """将服务器配置保存到 JSON 文件"""
    try:
        with open(filename, 'w') as f:
            json.dump(config, f, indent=4)
        logger.info(f"[TDX 数据服务 配置] 配置已成功保存到 '{filename}'。")
    except Exception as e:
        logger.error(f"[TDX 数据服务 配置] 保存配置到 '{filename}' 时出错: {e}")
# --- 结束配置函数 ---

redis_config = load_redis_config(REDIS_CONFIG_FILE)
redis_client = redis.StrictRedis(
    host=redis_config.get('host', 'localhost'),
    port=redis_config.get('port', 6379),
    password=redis_config.get('password', None),
    db=redis_config.get('db', 0),
    decode_responses=True,
    username=redis_config.get('username', None)
)

os.environ['PYTHONIOENCODING'] = 'utf-8'
try:
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
except Exception as e:
    # 某些环境（如 pm2/Windows 服务）下 sys.stdout 没有 buffer 属性，直接跳过
    pass

# 创建 Flask 应用
app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False  # 确保 JSON 响应使用 UTF-8
app.config['JSONIFY_MIMETYPE'] = "application/json; charset=utf-8"

# --- 全局变量存储实际使用的 TDX 地址 (区分 HQ 和 ExHq) ---
HQ_TDX_IP: Optional[str] = None
HQ_TDX_PORT: Optional[int] = None
HQ_TDX_SOURCE: str = "(未初始化)"
EXHQ_TDX_IP: Optional[str] = None
EXHQ_TDX_PORT: Optional[int] = None
EXHQ_TDX_SOURCE: str = "(未初始化)"
# ---------------------------------------------------------


# --- Market 映射 (Annotated with API type) ---
MARKET_MAP: Dict[str, int] = { # Explicit type hint
    # === Standard HQ API Markets ===
    'sz': 0,    # 深圳 A 股 (Hq)
    'sh': 1,    # 上海 A 股 / 指数 (Hq)
    'bj': 2,    # 北京 A 股 (Hq)
    # === Extended ExHq API Markets ===
    'hk': 31,   # 香港股票 (ExHq)
    'us': 74,   # 美国股票 (ExHq - 需要服务器支持)
    'shfe': 30, # 上期所 (商品) (ExHq)
    'dce': 28,  # 大商所 (商品) (ExHq)
    'czce': 29, # 郑商所 (商品) (ExHq)
    'cffex': 47, # 中金所 (金融期货) (ExHq)
    'ine': 48,  # 上海国际能源交易中心 (ExHq)
    'gfex': 49, # 广期所 (ExHq - 代码待确认)
    # --- 其他市场 (需要确认 API 类型和代码) ---
    'temp': 1, # 临时股 (ExHq)
    'zjs': 4,  # 郑州商品期权 (ExHq)
    'djs': 5,  # 大连商品期权 (ExHq)
    'sjs': 6,  # 上海商品期权 (ExHq)
    'sgo': 8,  # 上海个股期权 (ExHq)
    'hk_idx': 27, # 香港指数 (ExHq)
    'hk_main': 31, # 香港主板 (ExHq) - 重复 hk
    'hk_warr': 32, # 香港权证 (ExHq)
    'open_fund': 33, # 开放式基金 (ExHq)
    'money_fund': 34, # 货币型基金 (ExHq)
    'cm_fin': 35, # 招商理财产品 (ExHq)
    'cm_mon': 36, # 招商货币产品 (ExHq)
    'intl_idx': 37, # 国际指数 (ExHq)
    'macro_cn': 38, # 国内宏观指标 (ExHq)
    'cn_concept': 40, # 中国概念股 (ExHq)
    'us_famous': 41, # 美股知名公司 (ExHq) - 重复 us?
    'b_to_h': 43, # B股转H股 (ExHq)
    'transfer': 44, # 股份转让 (ExHq)
    'futures_idx': 47, # 股指期货 (ExHq) - 重复 cffex
    'hk_gem': 48, # 香港创业板 (ExHq)
    'hk_trust': 49, # 香港信托基金 (ExHq)
    'bond_pre': 54, # 国债预发行 (ExHq)
    'futures_main': 60, # 主力期货合约 (ExHq)
    'csi_idx': 62, # 中证指数 (ExHq)
    'hk_connect': 71, # 港股通 (ExHq)
}

def normalize_market_code(market_str):
    """
    标准化市场代码，将完整名称转换为简短格式
    Args:
        market_str (str): 输入的市场代码
    Returns:
        str: 标准化后的市场代码
    """
    if not market_str:
        return market_str

    market_lower = market_str.lower()

    # 标准化映射：完整名称 -> 简短格式
    normalization_map = {
        'sse': 'sh',    # 上海证券交易所 -> 上海
        'szse': 'sz',   # 深圳证券交易所 -> 深圳
    }

    # 如果在标准化映射中，返回标准格式
    if market_lower in normalization_map:
        logger.info(f"[市场代码标准化] {market_str} -> {normalization_map[market_lower]}")
        return normalization_map[market_lower]

    # 否则保持原样
    return market_lower

EXPECTED_MARKET_STRS = " 或 ".join(f"'{k}'" for k in MARKET_MAP.keys())
# --------------------

# --- Period 映射 (包括 ExHq 的 TDXParams) ---
PERIOD_MAP: Dict[str, Dict[str, Optional[int]]] = {
    '1m': {'hq': 8, 'ex': TDXParams.KLINE_TYPE_1MIN}, # pytdx TdxHq_API uses 8 for 1min
    '5m': {'hq': 0, 'ex': TDXParams.KLINE_TYPE_5MIN},
    '15m': {'hq': 1, 'ex': TDXParams.KLINE_TYPE_15MIN},
    '30m': {'hq': 2, 'ex': TDXParams.KLINE_TYPE_30MIN},
    '1h': {'hq': 3, 'ex': TDXParams.KLINE_TYPE_1HOUR},
    '60m': {'hq': 3, 'ex': TDXParams.KLINE_TYPE_1HOUR}, # Alias
    '1D': {'hq': 4, 'ex': TDXParams.KLINE_TYPE_DAILY},
    '1W': {'hq': 5, 'ex': TDXParams.KLINE_TYPE_WEEKLY},
    '1M': {'hq': 6, 'ex': TDXParams.KLINE_TYPE_MONTHLY},
    '1Q': {'hq': 10, 'ex': TDXParams.KLINE_TYPE_3MONTH}, # Quarter
    '1Y': {'hq': 11, 'ex': TDXParams.KLINE_TYPE_YEARLY}, # Year
}
# Add integer versions for HQ (assuming EX uses constants)
for i in [8, 0, 1, 2, 3, 4, 5, 6, 9, 10, 11]: # 9=daily, used by some servers for HQ
     period_str = str(i)
     if period_str not in PERIOD_MAP:
         # Assigning dict[str, Optional[int]] which matches the annotation
         PERIOD_MAP[period_str] = {'hq': i, 'ex': None}

DEFAULT_PERIOD_STR = '1D'
DEFAULT_PERIOD = PERIOD_MAP[DEFAULT_PERIOD_STR] # Now a dict {'hq': 4, 'ex': ...}


# --- 时区定义 ---
SHANGHAI_TZ = pytz.timezone('Asia/Shanghai')
# ----------------

# --- 全局Tick数据处理器实例 ---
tick_handler: Optional[TickDataHandler] = None
# ----------------

# --- API Type Mapping ---
def get_api_type(market_code: int) -> str: # Added type hint
    # Standard HQ markets (SZ, SH, BJ A-shares/indices)
    if market_code in [0, 1, 2]:
        return 'hq'
    # Most others seem to be Extended ExHq
    else:
        return 'ex'

# --- Connection Helpers (修改为使用新的全局变量) ---
def get_tdx_hq_api() -> Optional[TdxHq_API]:
    """创建并连接标准行情 TdxHq_API 实例。"""
    global HQ_TDX_IP, HQ_TDX_PORT, HQ_TDX_SOURCE # 使用 HQ 专用变量
    connect_ip = HQ_TDX_IP
    connect_port = HQ_TDX_PORT
    source = HQ_TDX_SOURCE
    if not connect_ip or not connect_port:
        logger.error("[TDX 数据服务 Hq] 错误：Hq 连接地址未初始化或无效!")
        return None
    api = TdxHq_API(heartbeat=True)
    logger.info(f"[TDX 数据服务 Hq] 尝试连接: {connect_ip}:{connect_port} {source}")
    try:
        if api.connect(connect_ip, connect_port):
            logger.info("[TDX 数据服务 Hq] 连接成功")
            return api
        else:
            logger.error(f"[TDX 数据服务 Hq] 使用地址 {connect_ip}:{connect_port} {source} 连接失败")
            return None
    except Exception as e_conn:
         logger.error(f"[TDX 数据服务 Hq] 连接 {connect_ip}:{connect_port} 时发生异常: {e_conn}")
         return None

def get_tdx_exhq_api() -> Optional[TdxExHq_API]:
    """创建并连接扩展行情 TdxExHq_API 实例。"""
    global EXHQ_TDX_IP, EXHQ_TDX_PORT, EXHQ_TDX_SOURCE # 使用 ExHq 专用变量
    connect_ip = EXHQ_TDX_IP
    connect_port = EXHQ_TDX_PORT
    source = EXHQ_TDX_SOURCE
    if not connect_ip or not connect_port:
        logger.error("[TDX 数据服务 ExHq] 错误：ExHq 连接地址未初始化或无效!")
        return None
    api = TdxExHq_API(heartbeat=True)
    logger.info(f"[TDX 数据服务 ExHq] 尝试连接: {connect_ip}:{connect_port} {source}")
    try:
        if api.connect(connect_ip, connect_port):
            logger.info("[TDX 数据服务 ExHq] 连接成功")
            return api
        else:
            logger.error(f"[TDX 数据服务 ExHq] 使用地址 {connect_ip}:{connect_port} {source} 连接失败")
            return None
    except Exception as e_conn:
        logger.error(f"[TDX 数据服务 ExHq] 连接 {connect_ip}:{connect_port} 时发生异常: {e_conn}")
        return None
# --- End Connection Helpers ---

# --- 动态发现函数 (修改为同时查找 HQ 和 ExHq) ---
def get_tdx_connection_info() -> Dict[str, Optional[Dict[str, Any]]]:
    """
    尝试通过 psutil 查找 tdxw.exe 进程及其建立的行情连接 (端口 7709 和 7727)。
    返回包含 'hq' 和 'exhq' 键的字典，每个键对应 {'ip': str, 'port': int} 或 None。
    """
    target_exe = 'tdxw.exe'
    hq_port = 7709
    exhq_port = 7727
    results: Dict[str, Optional[Dict[str, Any]]] = {'hq': None, 'exhq': None}

    try:
        found_proc = None
        # 1. 查找 tdxw.exe 进程
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_name = proc.info['name']
                if proc_name and target_exe.lower() in proc_name.lower():
                    found_proc = proc
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
            except Exception as e_proc_find:
                logger.warning(f"[TDX 数据服务 发现] 查找进程时出错: {e_proc_find}")
                continue

        # 2. 如果找到进程，检查其连接
        if found_proc:
            hq_found = False
            exhq_found = False
            try:
                for conn in found_proc.connections(kind='tcp'):
                    if conn.status == psutil.CONN_ESTABLISHED and conn.raddr:
                        remote_ip = conn.raddr.ip
                        remote_port = conn.raddr.port
                        # 检查是否为 HQ 端口
                        if not hq_found and remote_port == hq_port:
                            results['hq'] = {'ip': remote_ip, 'port': remote_port}
                            hq_found = True
                        # 检查是否为 ExHq 端口
                        elif not exhq_found and remote_port == exhq_port:
                            results['exhq'] = {'ip': remote_ip, 'port': remote_port}
                            exhq_found = True

                    if hq_found and exhq_found:
                        break

            except (psutil.AccessDenied):
                logger.warning(f"[TDX 数据服务 发现] 无权限访问进程 PID {found_proc.pid} 的连接信息。")
            except Exception as e_conn_find:
                logger.warning(f"[TDX 数据服务 发现] 检查进程 PID {found_proc.pid} 连接时出错: {e_conn_find}")

    except Exception as e_global:
        logger.error(f"[TDX 数据服务 发现] 在检测通达信连接过程中发生错误: {e_global}", exc_info=True)

    return results
# --- 结束动态发现函数 ---


# --- Flask 路由 ---
@app.route('/kline', methods=['GET'])
def get_kline():
    """
    获取 K 线数据 (支持标准和扩展行情)
    参数:
        market (str): 市场代码 (例如: 'sz', 'sh', 'hk', 'us', 'shfe', 或对应的整数代码如 '0', '1', '31') - **必需**
        code (str): 证券/合约代码 - **必需**
        period (str): 数据周期 (例如: '1m', ..., 'day') - 默认 'day'
        count (int): 获取 K 线数量 (默认: 800, 最大: 800)
        start (int): 起始位置 (默认为 0, 最新 K 线)
    返回:
        JSON: K 线数据列表 或错误信息
    """
    market_str_raw = request.args.get('market', type=str)
    code = request.args.get('code', type=str)
    period_str = request.args.get('period', default=DEFAULT_PERIOD_STR, type=str)
    count = request.args.get('count', default=800, type=int)
    start = request.args.get('start', default=0, type=int)

    # --- 参数校验：强制 count 不超过 800 ---
    if count > 800:
        logger.warning(f"[TDX 数据服务 /kline] 请求的 count ({count}) 超过最大值 800，将自动修正为 800。")
        count = 800

    logger.info(f"[TDX 数据服务 /kline] 请求: market={market_str_raw}, code={code}, period={period_str}, count={count}, start={start}")

    # --- 市场代码解析 (应用标准化) ---
    market_code: Optional[int] = None # Add type hint
    if market_str_raw:
        try:
            market_int = int(market_str_raw)
            if market_int >= 0: market_code = market_int
        except ValueError:
            # 先标准化市场代码，再查找映射
            normalized_market = normalize_market_code(market_str_raw)
            market_code = MARKET_MAP.get(normalized_market)
    # --- 结束市场代码解析 ---

    if market_code is None or code is None:
        param_errors = []
        if market_code is None:
             param_errors.append(f"无效或缺失 'market' 参数 (收到: '{market_str_raw}', 期望: {EXPECTED_MARKET_STRS} 或对应的整数代码)")
        if code is None:
             param_errors.append("缺失 'code' 参数")
        error_msg = ", ".join(param_errors)
        logger.warning(f"[TDX 数据服务 /kline] 请求参数错误: {error_msg}")
        return jsonify({"error": f"请求参数错误: {error_msg}"}), 400

    # --- 周期代码解析 (区分 Hq 和 ExHq) ---
    period_info = PERIOD_MAP.get(period_str)
    if period_info is None:
        logger.warning(f"[TDX 数据服务 /kline] 无效的 period 参数: '{period_str}'")
        return jsonify({"error": f"Invalid period parameter: '{period_str}'"}), 400

    api_type = get_api_type(market_code)
    period_code = period_info.get(api_type)
    if period_code is None and api_type == 'ex':
        period_code = period_info.get('hq') # Fallback for EX if specific code missing
        logger.warning(f"[TDX 数据服务 /kline] 周期 '{period_str}' 缺少 ExHq 代码，尝试使用 Hq 代码 {period_code}")
    if period_code is None:
         logger.error(f"[TDX 数据服务 /kline] 无法确定周期代码 for period='{period_str}', api_type='{api_type}'")
         return jsonify({"error": f"Cannot determine period code for {period_str} and market {market_code}"}), 400
    # --- 结束周期代码解析 ---

    logger.info(f"[TDX 数据服务 /kline] 使用 API 类型: {api_type}, 市场代码: {market_code}, 周期代码: {period_code}")

    # --- 获取 API 实例 ---
    api: Optional[Union[TdxHq_API, TdxExHq_API]] = None # Type hint for api
    if api_type == 'hq':
        api = get_tdx_hq_api()
    elif api_type == 'ex':
        api = get_tdx_exhq_api()
    # --- 结束获取 API ---

    if not api:
        return jsonify({"error": f"Failed to connect to TDX {api_type.upper()} server"}), 500

    data = None
    df: Optional[pd.DataFrame] = None # Type hint for df
    try:
        # --- 判断是否为指数，并调用对应的 API 方法 ---
        use_index_bars = False

        # 规则 1: 检查专门的指数市场代码 (根据 MARKET_MAP 补充)
        # 27:hk_idx, 47:futures_idx/cffex, 62:csi_idx
        if market_code in [27, 47, 62]:
            use_index_bars = True
            logger.info(f"[TDX 数据服务 /kline] 市场代码 {market_code} 为专用指数市场")

        # 规则 2: 检查特定市场的代码格式 (主要针对沪深)
        elif market_code == 1: # 上海 (SH)
            # 常见的上证指数开头: 000, 880 (分类指数), 999 (全指数)
            if code == '000001' or code.startswith(('000', '880', '999')):
                use_index_bars = True
                logger.info(f"[TDX 数据服务 /kline] 检测到上海指数代码格式: {code}")
        elif market_code == 0: # 深圳 (SZ)
            # 常见的深证指数开头: 399
            if code.startswith('399'):
                use_index_bars = True
                logger.info(f"[TDX 数据服务 /kline] 检测到深圳指数代码格式: {code}")

        # (可以根据需要添加其他市场的指数判断规则, e.g., HK, US, Futures)

        # 调用参数，输入日志
        logger.info(f"[TDX 数据服务 /kline] 调用参数: period_code={period_code}, market_code={market_code}, code='{code}', start={start}, count={count}")

        # 根据判断结果调用 API
        if use_index_bars and api_type == 'hq': # 只有 HQ API 有专门的 get_index_bars
            logger.info(f"[TDX 数据服务 /kline] 调用 pytdx API: get_index_bars(category={period_code}, market={market_code}, code='{code}', start={start}, count={count})")
            assert isinstance(api, TdxHq_API)
            data = api.get_index_bars(period_code, market_code, code, start, count)
        else:
            # 对于非指数，或指数但 API 类型是 ExHq (没有 get_index_bars)
            if api_type == 'hq':
                logger.info(f"[TDX 数据服务 /kline] 调用 pytdx API: get_security_bars(category={period_code}, market={market_code}, code='{code}', start={start}, count={count})")
                assert isinstance(api, TdxHq_API)
                data = api.get_security_bars(period_code, market_code, code, start, count)
            elif api_type == 'ex':
                log_msg_verb = "调用" if not use_index_bars else "仍尝试用"
                logger.info(f"[TDX 数据服务 /kline] {log_msg_verb} pytdx API: get_instrument_bars(category={period_code}, market={market_code}, code='{code}', start={start}, count={count}) (API Type: ExHq)")
                assert isinstance(api, TdxExHq_API)
                data = api.get_instrument_bars(period_code, market_code, code, start, count)
        # --- 结束 API 调用 ---

        if data:
            df = api.to_df(data)

            # --- 添加日志：检查原始日期/时间列 (特别是指数) ---
            datetime_col_name: Optional[str] = None # Type hint
            original_datetime_col_name_for_output: Optional[str] = None # Type hint
            if 'datetime' in df.columns: datetime_col_name = 'datetime'
            elif 'date' in df.columns: datetime_col_name = 'date'

            if datetime_col_name:
                 logger.info(f"[TDX 数据服务 /kline] 原始 '{datetime_col_name}' 列内容 (前5条) for market={market_code}, code={code}:\n{df[datetime_col_name].head().to_string()}")
                 logger.info(f"[TDX 数据服务 /kline] 原始 '{datetime_col_name}' 列类型: {df[datetime_col_name].dtype}")


            # --- 数据清洗 (vol, amount - 保持不变) ---
            small_value_threshold = 1e-8
            if 'vol' in df.columns:
                # Ensure vol is numeric before applying calculations
                df['vol'] = pd.to_numeric(df['vol'], errors='coerce')
                df['vol'] = df['vol'].apply(lambda x: 0.0 if pd.isnull(x) or (0 <= x < small_value_threshold or x < 0) else x)
            if 'amount' in df.columns:
                df['amount'] = pd.to_numeric(df['amount'], errors='coerce')
                df['amount'] = df['amount'].apply(lambda x: 0.0 if pd.isnull(x) or (0 <= x < small_value_threshold or x < 0) else x)
            # --- 结束数据清洗 ---

            df['timestamp'] = None # 初始化

            if datetime_col_name:
                original_datetime_col_name_for_output = datetime_col_name
                try:
                    # --- 日期时间处理 (保持不变，依赖 to_shanghai_timestamp 的鲁棒性) ---
                    dt_series = pd.to_datetime(df[datetime_col_name], errors='coerce') # 关键: coerce 错误

                    # --- 添加诊断日志 ---
                    logger.info(f"[TDX 数据服务 /kline] 诊断: 解析后的 dt_series (前5条):\n{dt_series.head().to_string()}")
                    logger.info(f"[TDX 数据服务 /kline] 诊断: 解析后的 dt_series (后5条):\n{dt_series.tail().to_string()}")
                    # 对比原始字符串
                    logger.info(f"[TDX 数据服务 /kline] 诊断: 原始字符串 (前5条):\n{df[datetime_col_name].head().to_string()}")
                    logger.info(f"[TDX 数据服务 /kline] 诊断: 原始字符串 (后5条):\n{df[datetime_col_name].tail().to_string()}")
                    # ---------------------

                    # 2. 计算秒级 Unix 时间戳 (明确指定上海时区)
                    def to_shanghai_timestamp(dt: Any) -> Optional[int]: # Add type hint
                        if pd.isnull(dt): # 处理 NaT 或 None
                            return None
                        try:
                            # Ensure dt is a datetime object
                            if not isinstance(dt, (pd.Timestamp, datetime.datetime)):
                                dt = pd.to_datetime(dt) # Attempt conversion if not already datetime
                                if pd.isnull(dt): return None # Conversion failed

                            # 如果 dt 是 naive (无时区), 则假定它是上海时间并本地化
                            if dt.tzinfo is None or dt.tzinfo.utcoffset(dt) is None:
                                dt_aware = SHANGHAI_TZ.localize(dt)
                            else:
                                # 如果已经是 aware, 确保它是上海时区 (或转换为上海时区)
                                dt_aware = dt.astimezone(SHANGHAI_TZ)
                            # 返回 UTC 时间戳 (秒)
                            return int(dt_aware.timestamp())
                        except (pytz.exceptions.NonExistentTimeError, pytz.exceptions.AmbiguousTimeError, OverflowError, ValueError) as tz_err:
                            logger.warning(f"时间戳转换错误 (时区相关): {tz_err} for datetime {dt}")
                            return None # 返回 None 表示转换失败
                        except Exception as other_err:
                             logger.warning(f"时间戳转换时发生未知错误: {other_err} for datetime {dt}")
                             return None

                    df['timestamp'] = dt_series.apply(to_shanghai_timestamp)

                    # 3. 将原始日期/时间列格式化为字符串 (覆盖原列)
                    def format_datetime_str(dt: Any) -> Optional[str]: # Add type hint
                         if pd.isnull(dt):
                              return None
                         try:
                              # Ensure dt is datetime before formatting
                              if not isinstance(dt, (pd.Timestamp, datetime.datetime)):
                                   dt = pd.to_datetime(dt)
                                   if pd.isnull(dt): return None

                              if datetime_col_name == 'datetime':
                                   return dt.strftime('%Y-%m-%d %H:%M:%S')
                              else: # date
                                   return dt.strftime('%Y-%m-%d')
                         except Exception:
                              return None # 格式化失败也返回 None

                    df[datetime_col_name] = dt_series.apply(format_datetime_str)
                    del dt_series
                except Exception as dt_error:
                    logger.warning(f"[TDX 数据服务 /kline] 处理日期时间或时间戳时出错: {dt_error}. code={code}", exc_info=True)
                    # Ensure timestamp column exists even on error
                    if 'timestamp' not in df.columns: df['timestamp'] = None
                    if original_datetime_col_name_for_output and original_datetime_col_name_for_output not in df.columns:
                         df[original_datetime_col_name_for_output] = None # Add back if missing
            else:
                 logger.warning(f"[TDX 数据服务 /kline] 未找到 'datetime' 或 'date' 列 for market={market_code}, code={code}")


            # --- 成交量转手数 (保持不变) ---
            if 'vol' in df.columns:
                # 确保 vol 列是数值类型
                df['vol'] = pd.to_numeric(df['vol'], errors='coerce').fillna(0)
                # 使用 // 100 进行整数除法，得到手数
                # Note: ensure the column is actually numeric first
                df['vol'] = df['vol'].apply(lambda x: int(x // 100) if pd.notnull(x) else 0)
                logger.info("[TDX 数据服务 /kline] 成交量已从 '股' 转换为 '手'")
            # --- 结束成交量转换 ---

            # --- 选择并重命名列 (保持不变) ---
            required_columns = {
                'timestamp': 'time',
                'open': 'open',
                'high': 'high',
                'low': 'low',
                'close': 'close',
                'vol': 'volume',
            }
            if original_datetime_col_name_for_output and original_datetime_col_name_for_output in df.columns:
                required_columns[original_datetime_col_name_for_output] = original_datetime_col_name_for_output
            columns_to_select = {k: v for k, v in required_columns.items() if k in df.columns}
            # Ensure all target columns exist in the DataFrame before selection
            missing_cols = set(columns_to_select.keys()) - set(df.columns)
            if missing_cols:
                 logger.warning(f"[TDX 数据服务 /kline] 输出时缺少列: {missing_cols}. 将不会包含在结果中。")
                 columns_to_select = {k: v for k, v in columns_to_select.items() if k in df.columns}

            df_output = df[list(columns_to_select.keys())].rename(columns=columns_to_select)
            # --- 结束列选择 ---

            logger.info(f"[TDX 数据服务 /kline] K 线数据获取成功: code={code}, count={len(df_output)}")
            df_output = df_output.where(pd.notnull(df_output), None) # 将 NaN 转为 None
            return jsonify(df_output.to_dict('records'))
        else:
            logger.warning(f"[TDX 数据服务 /kline] API 未返回数据: market={market_code}, code={code}, period={period_code}")
            return jsonify([])
    except Exception as e:
        logger.error(f"[TDX 数据服务 /kline] 获取 K 线数据时出错: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500
    finally:
        if api:
            # Use getattr to safely check for disconnect method
            disconnect_method = getattr(api, 'disconnect', None)
            if callable(disconnect_method):
                try:
                    disconnect_method()
                    logger.info(f"[TDX 数据服务 /kline] 断开 {api_type.upper()} 连接")
                except Exception as disconn_err:
                     logger.error(f"[TDX 数据服务 /kline] 断开 {api_type.upper()} 连接时出错: {disconn_err}")
            else:
                 logger.warning(f"[TDX 数据服务 /kline] 无法断开 {api_type.upper()} 连接: 未找到 disconnect 方法")


@app.route('/quote', methods=['GET'])
def get_quote():
    """
    获取实时盘口数据 (支持标准和扩展行情)
    参数:
        market (str): 市场代码 (例如: 'sz', 'sh', '31', 或逗号分隔) - **必需**
        code (str): 证券/合约代码 (对应 market 参数，或逗号分隔) - **必需**
    返回:
        JSON: 盘口数据列表或错误信息
    """
    market_param = request.args.get('market', type=str)
    code_param = request.args.get('code', type=str)

    logger.info(f"[TDX 数据服务 /quote] 请求: market={market_param}, code={code_param}")

    if not market_param or not code_param:
        logger.warning("[TDX 数据服务 /quote] 盘口请求缺少 market 或 code 参数")
        return jsonify({"error": "Missing required parameters: market, code"}), 400

    codes = code_param.split(',')
    market_strs = [m.strip() for m in market_param.split(',') if m.strip()] # 保留原始字符串用于尝试整数转换

    # 转换 market 字符串为整数代码 (优先尝试整数)
    market_codes = []
    invalid_markets = []
    for m_str in market_strs:
        m_code = None
        try:
            m_int = int(m_str)
            if m_int >= 0:
                m_code = m_int
            else:
                pass # 无效整数，继续尝试映射
        except ValueError:
            # 转换整数失败，先标准化再尝试字符串映射
            normalized_market = normalize_market_code(m_str)
            m_code = MARKET_MAP.get(normalized_market)
        
        if m_code is None:
            invalid_markets.append(m_str) # 添加原始无效字符串
        else:
            market_codes.append(m_code)

    if invalid_markets:
        logger.warning(f"[TDX 数据服务 /quote] 盘口请求包含无效 market 值: {invalid_markets}")
        return jsonify({"error": f"Invalid market value(s): {invalid_markets}. Use one of {EXPECTED_MARKET_STRS} or their integer codes"}), 400

    # 检查 code 和 market 数量是否匹配或可广播
    if len(codes) != len(market_codes):
         # 如果市场和代码数量不匹配，尝试使用第一个市场代码应用于所有证券代码
        if len(market_codes) == 1 and len(codes) > 1:
            logger.warning("[TDX 数据服务 /quote] 市场代码数量与证券代码数量不匹配，将使用第一个市场代码应用于所有证券")
            market_codes = [market_codes[0]] * len(codes) # 使用转换后的整数代码
        else:
            logger.error("[TDX 数据服务 /quote] 市场代码数量与证券代码数量不匹配 (转换后)")
            return jsonify({"error": "Number of markets must match number of codes, or provide a single market for all codes"}), 400

    # 此时 market_codes 已经是整数列表
    try:
        market_code_pairs = list(zip(market_codes, codes))
    except Exception as e_pair:
        logger.error(f"[TDX 数据服务 /quote] 创建 market-code 对时出错: {e_pair}")
        return jsonify({"error": "Internal error creating market-code pairs"}), 500

    all_results = []
    errors = []
    api_hq = None
    api_ex = None

    # --- 根据 API 类型分组请求 ---
    hq_pairs = []
    ex_pairs = []
    for market_id, stock_code in market_code_pairs:
        api_type = get_api_type(market_id)
        if api_type == 'hq':
            hq_pairs.append((market_id, stock_code))
        else: # 'ex'
            ex_pairs.append((market_id, stock_code))
    # --- 结束分组 ---

    try:
        # --- 处理 Hq 请求 --- 
        if hq_pairs:
            logger.info(f"[TDX 数据服务 /quote] 发起 Hq 请求: {len(hq_pairs)} 对")
            api_hq = get_tdx_hq_api()
            if api_hq:
                try:
                    # Hq 支持批量查询
                    logger.info(f"[TDX 数据服务 /quote] 调用 pytdx API: get_security_quotes(market_code_pairs={hq_pairs})")
                    data_hq = api_hq.get_security_quotes(hq_pairs)
                    if data_hq:
                        all_results.extend(data_hq)
                        logger.info(f"[TDX 数据服务 /quote] Hq 数据获取成功: {len(data_hq)} 条")
                    else:
                        logger.warning("[TDX 数据服务 /quote] Hq API 未返回数据")
                except Exception as e_hq:
                    logger.error(f"[TDX 数据服务 /quote] 获取 Hq 盘口数据时出错: {e_hq}", exc_info=True)
                    errors.append(f"Hq quote error: {e_hq}")
                finally:
                    if api_hq and getattr(api_hq, 'client', None):
                       api_hq.disconnect()
                       logger.info("[TDX 数据服务 /quote] 断开 Hq 连接")
            else:
                errors.append("Failed to connect to TDX Hq server")
        # --- 结束 Hq 处理 ---

        # --- 处理 ExHq 请求 --- 
        if ex_pairs:
            logger.info(f"[TDX 数据服务 /quote] 发起 ExHq 请求: {len(ex_pairs)} 对")
            api_ex = get_tdx_exhq_api()
            if api_ex:
                try:
                    for market_id, stock_code in ex_pairs:
                        try:
                            # ExHq 一次只查一个
                            logger.info(f"[TDX 数据服务 /quote] 调用 pytdx API: get_instrument_quote(market={market_id}, code='{stock_code}')")
                            data_single_ex = api_ex.get_instrument_quote(market_id, stock_code)
                            if data_single_ex:
                                all_results.append(data_single_ex)
                            else:
                                logger.warning(f"[TDX 数据服务 /quote] ExHq API 未返回数据 for ({market_id}, {stock_code})")
                        except Exception as e_single_ex:
                            logger.error(f"[TDX 数据服务 /quote] 获取 ExHq 盘口数据时出错 for ({market_id}, {stock_code}): {e_single_ex}", exc_info=True)
                            errors.append(f"ExHq quote error for ({market_id}, {stock_code}): {e_single_ex}")
                    logger.info(f"[TDX 数据服务 /quote] ExHq 数据获取完成 (可能部分失败)")
                finally:
                    if api_ex and getattr(api_ex, 'client', None):
                        api_ex.disconnect()
                        logger.info("[TDX 数据服务 /quote] 断开 ExHq 连接")
            else:
                errors.append("Failed to connect to TDX ExHq server")
        # --- 结束 ExHq 处理 ---

        # --- 返回结果或错误 --- 
        if errors:
            # 如果有错误，也尽量返回成功获取的数据
            logger.warning(f"[TDX 数据服务 /quote] 请求完成但出现错误: {errors}")
            # 可以选择将错误信息包含在响应中，或只返回成功部分
            # return jsonify({"data": all_results, "errors": errors})
            # 暂时只返回成功获取的数据
            if not all_results: # 如果完全没有成功的数据，返回错误
                 return jsonify({"error": "; ".join(errors)}), 500

        logger.info(f"[TDX 数据服务 /quote] 请求成功完成，返回 {len(all_results)} 条数据")
        # Ensure NaN is converted to null for JSON compatibility
        # Helper function to replace NaN in nested structures
        def replace_nan_with_none(obj):
            if isinstance(obj, list):
                return [replace_nan_with_none(x) for x in obj]
            elif isinstance(obj, dict):
                return {k: replace_nan_with_none(v) for k, v in obj.items()}
            elif isinstance(obj, float) and pd.isna(obj):
                 return None
            return obj

        cleaned_results = replace_nan_with_none(all_results)
        return jsonify(cleaned_results)
        # --- 结束结果处理 ---

    except Exception as e_global: # Catch any unexpected error during processing
         logger.error(f"[TDX 数据服务 /quote] 处理盘口请求时发生全局错误: {e_global}", exc_info=True)
         # Ensure connections are closed if they were opened
         if api_hq and getattr(api_hq, 'client', None): api_hq.disconnect()
         if api_ex and getattr(api_ex, 'client', None): api_ex.disconnect()
         return jsonify({"error": f"Global error processing quote request: {e_global}"}), 500


@app.route('/tick', methods=['GET'])
def get_tick():
    """
    获取实时tick数据 (基于实时报价的tick数据)
    参数:
        market (str): 市场代码 (例如: 'sz', 'sh', 'hk', 'us', 'shfe') - **必需**
        code (str): 证券/合约代码 - **必需**
    返回:
        JSON: tick数据或错误信息
    """
    global tick_handler

    market_param = request.args.get('market', type=str)
    code_param = request.args.get('code', type=str)

    logger.info(f"[TDX 数据服务 /tick] 请求: market={market_param}, code={code_param}")

    if not market_param or not code_param:
        logger.warning("[TDX 数据服务 /tick] tick请求缺少 market 或 code 参数")
        return jsonify({"error": "Missing required parameters: market, code"}), 400

    if not tick_handler:
        logger.error("[TDX 数据服务 /tick] Tick数据处理器未初始化")
        return jsonify({"error": "Tick data handler not initialized"}), 500

    try:
        # 标准化市场代码后获取tick数据
        normalized_market = normalize_market_code(market_param)
        tick_data = tick_handler.get_real_tick_data(normalized_market, code_param)

        if tick_data:
            logger.info(f"[TDX 数据服务 /tick] Tick数据获取成功: {market_param}:{code_param}")
            return jsonify(tick_data)
        else:
            logger.warning(f"[TDX 数据服务 /tick] 未获取到tick数据: {market_param}:{code_param}")
            return jsonify({"error": "No tick data available"}), 404

    except Exception as e:
        logger.error(f"[TDX 数据服务 /tick] 获取tick数据时出错: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500


@app.route('/tick/history', methods=['GET'])
def get_tick_history():
    """
    获取历史tick数据 (基于成交明细的历史tick数据)
    参数:
        market (str): 市场代码 (例如: 'sz', 'sh', 'hk', 'us', 'shfe') - **必需**
        code (str): 证券/合约代码 - **必需**
        date (str): 日期 (YYYY-MM-DD格式) - 可选，默认今天
    返回:
        JSON: tick数据列表或错误信息
    """
    global tick_handler

    market_param = request.args.get('market', type=str)
    code_param = request.args.get('code', type=str)
    date_param = request.args.get('date', type=str)

    logger.info(f"[TDX 数据服务 /tick/history] 请求: market={market_param}, code={code_param}, date={date_param}")

    if not market_param or not code_param:
        logger.warning("[TDX 数据服务 /tick/history] 历史tick请求缺少 market 或 code 参数")
        return jsonify({"error": "Missing required parameters: market, code"}), 400

    if not tick_handler:
        logger.error("[TDX 数据服务 /tick/history] Tick数据处理器未初始化")
        return jsonify({"error": "Tick data handler not initialized"}), 500

    try:
        # 标准化市场代码后获取历史tick数据
        normalized_market = normalize_market_code(market_param)
        tick_list = tick_handler.get_historical_tick_data(normalized_market, code_param, date_param)

        logger.info(f"[TDX 数据服务 /tick/history] 历史tick数据获取成功: {market_param}:{code_param}, 数量: {len(tick_list)}")
        return jsonify(tick_list)

    except Exception as e:
        logger.error(f"[TDX 数据服务 /tick/history] 获取历史tick数据时出错: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

# 测试订阅健康
@app.route('/subscribe_health', methods=['POST'])
def subscribe_health():
    """
    测试订阅健康状态
    请求参数:
        key: 要检查的订阅key前缀
    返回:
        JSON: 健康状态信息
    """
    try:
        data = request.get_json()
        if not data or 'key' not in data:
            return jsonify({"success": False, "error": "缺少key参数"}), 400
        
        key = data['key']
        logger.info(f"[TDX 数据服务 /subscribe_health] 检查key: {key}")
        
        # 检查是否有以该key开头的订阅
        matching_subscriptions = [sub for sub in key_subscription_list if sub.startswith(key)]
        
        if matching_subscriptions:
            logger.info(f"[TDX 数据服务 /subscribe_health] 找到匹配的订阅: {len(matching_subscriptions)} 个")
            return jsonify({
                "success": True, 
                "result": True,
                "message": f"找到 {len(matching_subscriptions)} 个匹配的订阅",
                "matching_count": len(matching_subscriptions)
            })
        else:
            logger.warning(f"[TDX 数据服务 /subscribe_health] 未找到以 {key} 开头的订阅")
            return jsonify({
                "success": True, 
                "result": False,
                "message": f"未找到以 {key} 开头的订阅",
                "matching_count": 0
            })
            
    except Exception as e:
        logger.error(f"[TDX 数据服务 /subscribe_health] 健康检查时出错: {e}", exc_info=True)
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/subscriptions', methods=['GET'])
def get_subscriptions():
    """
    获取当前订阅状态
    返回:
        JSON: 订阅统计信息
    """
    global subscription_counters, subscriptions, client_subscriptions

    try:
        # 获取tick缓存统计
        cache_stats = get_tick_cache_stats()

        # 统计信息
        stats = {
            'total_symbols': len(subscription_counters),
            'total_clients': len(client_subscriptions),
            'active_symbols': {symbol: count for symbol, count in subscription_counters.items() if count > 0},
            'client_count_by_symbol': {symbol: len(clients) for symbol, clients in subscriptions.items()},
            'push_interval': PUSH_INTERVAL,
            'tick_cache': cache_stats
        }

        logger.info(f"[TDX 数据服务 /subscriptions] 返回订阅统计: {len(subscription_counters)} 个品种, {len(client_subscriptions)} 个客户端")
        return jsonify(stats)

    except Exception as e:
        logger.error(f"[TDX 数据服务 /subscriptions] 获取订阅状态时出错: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500


# --- Socket.IO 服务器和订阅管理 ---

# 全局变量 - 订阅管理
subscriptions = defaultdict(set)  # {symbol: {client_id1, client_id2, ...}}
client_subscriptions = defaultdict(set)  # {client_id: {symbol1, symbol2, ...}}
subscription_counters = defaultdict(int)  # {symbol: count} 订阅计数器
key_subscription_list = set()  # 维护key_symbol_period列表，避免重复订阅
tick_data_thread = None
tick_data_running = False

# 持久化相关
PERSISTENCE_FILE = 'tdxserver_persistence.json'

def save_persistence_data():
    """保存持久化数据到文件"""
    try:
        persistence_data = {
            'key_subscription_list': list(key_subscription_list),
            'subscription_counters': dict(subscription_counters),
            'kline_subscription_counters': {period: dict(counters) for period, counters in kline_subscription_counters.items()}
        }
        with open(PERSISTENCE_FILE, 'w', encoding='utf-8') as f:
            json.dump(persistence_data, f, ensure_ascii=False, indent=2)
        logger.info(f"[持久化] 数据已保存到 {PERSISTENCE_FILE}")
    except Exception as e:
        logger.error(f"[持久化] 保存数据失败: {e}")

def load_persistence_data():
    """从文件加载持久化数据"""
    try:
        if os.path.exists(PERSISTENCE_FILE):
            with open(PERSISTENCE_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 恢复key_subscription_list
            global key_subscription_list
            key_subscription_list = set(data.get('key_subscription_list', []))
            
            # 恢复subscription_counters
            global subscription_counters
            subscription_counters.clear()
            subscription_counters.update(data.get('subscription_counters', {}))
            
            # 恢复kline_subscription_counters
            global kline_subscription_counters
            kline_subscription_counters.clear()
            for period, counters in data.get('kline_subscription_counters', {}).items():
                kline_subscription_counters[period].update(counters)
            
            logger.info(f"[持久化] 数据已从 {PERSISTENCE_FILE} 恢复")
            logger.info(f"[持久化] key_subscription_list: {len(key_subscription_list)} 项")
            logger.info(f"[持久化] subscription_counters: {len(subscription_counters)} 项")
            logger.info(f"[持久化] kline_subscription_counters: {len(kline_subscription_counters)} 个周期")
        else:
            logger.info(f"[持久化] 持久化文件 {PERSISTENCE_FILE} 不存在，使用默认空数据")
    except Exception as e:
        logger.error(f"[持久化] 加载数据失败: {e}")
        logger.info("[持久化] 使用默认空数据")

# tick数据缓存和去重
last_tick_data = {}  # {symbol: last_tick_info} 保存每个品种的上一次tick数据
tick_data_lock = threading.Lock()  # 线程锁保护tick数据缓存

# 推送配置
PUSH_INTERVAL = 60.0  # 推送间隔（秒）

# 创建 Socket.IO 服务器
sio = socketio.AsyncServer(
    cors_allowed_origins="*",
    logger=True,
    engineio_logger=True,
    ping_interval=25,  # 25秒发送PING
    ping_timeout=60    # 60秒无响应则断开
)

# 创建 aiohttp 应用
aio_app = web.Application()
sio.attach(aio_app)

@sio.event
async def connect(sid, environ):
    """客户端连接事件"""
    logger.info(f"[TDX Socket.IO] 客户端连接: {sid}")
    await sio.emit('connected', {'message': 'TDX 数据服务连接成功'}, room=sid)

@sio.event
async def disconnect(sid):
    """客户端断开连接事件"""
    logger.info(f"[TDX Socket.IO] 客户端断开连接: {sid}")

    # 清理该客户端的所有订阅
    if sid in client_subscriptions:
        symbols_to_unsubscribe = client_subscriptions[sid].copy()
        for symbol in symbols_to_unsubscribe:
            await unsubscribe_symbol(sid, symbol)
        del client_subscriptions[sid]

    logger.info(f"[TDX Socket.IO] 客户端 {sid} 的所有订阅已清理")

@sio.event
async def subscribe_tick(sid, data):
    """订阅 tick 数据"""
    global subscription_counters

    try:
        symbol = data.get('symbol')
        market = data.get('market')

        if not symbol or not market:
            await sio.emit('error', {
                'message': '缺少必要参数: symbol 和 market'
            }, room=sid)
            return

        # 构建完整的品种标识
        full_symbol = f"{market}:{symbol}"

        # 检查是否已经订阅过
        if sid in subscriptions[full_symbol]:
            await sio.emit('error', {
                'message': f'已经订阅了 {full_symbol}'
            }, room=sid)
            return

        # 添加订阅
        subscriptions[full_symbol].add(sid)
        client_subscriptions[sid].add(full_symbol)

        # 订阅计数器 +1
        subscription_counters[full_symbol] += 1

        logger.info(f"[TDX Socket.IO] 客户端 {sid} 订阅品种: {full_symbol}, 当前订阅数: {subscription_counters[full_symbol]}")

        # 确保 tick 数据线程正在运行
        await ensure_tick_thread_running()

        # 保存持久化数据
        save_persistence_data()

        await sio.emit('subscribed', {
            'symbol': symbol,
            'market': market,
            'full_symbol': full_symbol,
            'subscription_count': subscription_counters[full_symbol],
            'message': f'成功订阅 {full_symbol}'
        }, room=sid)

    except Exception as e:
        logger.error(f"[TDX Socket.IO] 订阅错误: {e}")
        await sio.emit('error', {'message': f'订阅失败: {str(e)}'}, room=sid)

@sio.event
async def unsubscribe_tick(sid, data):
    """取消订阅 tick 数据"""
    global subscription_counters

    try:
        symbol = data.get('symbol')
        market = data.get('market')

        if not symbol or not market:
            await sio.emit('error', {
                'message': '缺少必要参数: symbol 和 market'
            }, room=sid)
            return

        full_symbol = f"{market}:{symbol}"

        # 检查是否确实订阅了
        if sid not in subscriptions[full_symbol]:
            await sio.emit('error', {
                'message': f'未订阅 {full_symbol}'
            }, room=sid)
            return

        # 执行退订
        await unsubscribe_symbol(sid, full_symbol)

        await sio.emit('unsubscribed', {
            'symbol': symbol,
            'market': market,
            'full_symbol': full_symbol,
            'subscription_count': subscription_counters[full_symbol],
            'message': f'成功取消订阅 {full_symbol}'
        }, room=sid)

    except Exception as e:
        logger.error(f"[TDX Socket.IO] 取消订阅错误: {e}")
        await sio.emit('error', {'message': f'取消订阅失败: {str(e)}'}, room=sid)

async def unsubscribe_symbol(sid, full_symbol):
    """取消订阅指定品种"""
    global subscription_counters

    if full_symbol in subscriptions:
        subscriptions[full_symbol].discard(sid)

        # 订阅计数器 -1
        if subscription_counters[full_symbol] > 0:
            subscription_counters[full_symbol] -= 1

        # 如果没有订阅者了，清理数据
        if not subscriptions[full_symbol]:
            del subscriptions[full_symbol]
            if subscription_counters[full_symbol] == 0:
                del subscription_counters[full_symbol]

    if sid in client_subscriptions:
        client_subscriptions[sid].discard(full_symbol)

    logger.info(f"[TDX Socket.IO] 客户端 {sid} 取消订阅品种: {full_symbol}, 剩余订阅数: {subscription_counters.get(full_symbol, 0)}")

    # 保存持久化数据
    save_persistence_data()

async def ensure_tick_thread_running():
    """确保 tick 数据线程正在运行"""
    global tick_data_thread, tick_data_running

    if tick_data_thread is None or not tick_data_thread.is_alive():
        tick_data_running = True
        tick_data_thread = threading.Thread(target=tick_data_worker, daemon=True)
        tick_data_thread.start()
        logger.info("[TDX Socket.IO] Tick 数据推送线程已启动")

def tick_data_worker():
    """Tick 数据推送工作线程"""
    global tick_data_running, subscription_counters
    logger.info("[tdxserver][退出监控] tick_data_worker 线程启动")
    # 创建新的事件循环用于这个线程
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    cleanup_counter = 0
    cleanup_interval = 60  # 每60个周期清理一次过期数据
    try:
        while tick_data_running:
            try:
                # 如果没有订阅，休眠
                if not subscription_counters:
                    time.sleep(PUSH_INTERVAL)
                    continue

                # 获取所有有订阅的品种（计数器 > 0）
                active_symbols = [symbol for symbol, count in subscription_counters.items() if count > 0]

                if not active_symbols:
                    time.sleep(PUSH_INTERVAL)
                    continue

                logger.debug(f"[TDX Socket.IO] 推送 {len(active_symbols)} 个品种的tick数据")

                # 处理每个活跃品种
                pushed_count = 0
                for full_symbol in active_symbols:
                    try:
                        # 解析品种信息
                        market, symbol = full_symbol.split(':', 1)

                        # 获取 tick 数据（包含去重逻辑）
                        tick_data = get_enhanced_tick_data(market, symbol)

                        if tick_data:
                            # 使用当前线程的事件循环广播数据
                            loop.run_until_complete(
                                broadcast_tick_data(full_symbol, tick_data)
                            )
                            pushed_count += 1

                    except Exception as e:
                        logger.error(f"[TDX Socket.IO] 获取 {full_symbol} tick 数据错误: {e}")
                        continue

                if pushed_count > 0:
                    logger.debug(f"[TDX Socket.IO] 实际推送了 {pushed_count} 个品种的新tick数据")

                # 定期清理过期的tick缓存
                cleanup_counter += 1
                if cleanup_counter >= cleanup_interval:
                    cleanup_old_tick_data()
                    cleanup_counter = 0

                # 控制推送频率
                time.sleep(PUSH_INTERVAL)

            except Exception as e:
                logger.error(f"[TDX Socket.IO] Tick 数据推送线程错误: {e}")
                time.sleep(PUSH_INTERVAL)
    except Exception as e:
        logger.error(f"[tdxserver][退出监控] tick_data_worker 线程异常退出: {e}", exc_info=True)
    finally:
        loop.close()
        logger.error("[tdxserver][退出监控] tick_data_worker 线程已退出")

async def broadcast_tick_data(full_symbol, tick_data):
    """广播 tick 数据给订阅的客户端"""
    if full_symbol in subscriptions:
        clients = subscriptions[full_symbol].copy()
        if clients:
            logger.debug(f"[TDX Socket.IO] 广播 {full_symbol} tick 数据给 {len(clients)} 个客户端")
            for client_id in clients:
                try:
                    await sio.emit('tick_data', {
                        'symbol': full_symbol,
                        'data': tick_data,
                        'timestamp': int(time.time() * 1000)
                    }, room=client_id)
                except Exception as e:
                    logger.error(f"[TDX Socket.IO] 发送数据给客户端 {client_id} 失败: {e}")

def get_tick_data(market, symbol):
    """获取指定品种的 tick 数据 (Socket.IO使用)"""
    global tick_handler

    if not tick_handler:
        logger.warning("[TDX Socket.IO] Tick数据处理器未初始化")
        return None

    try:
        # 标准化市场代码后使用tick处理器获取数据
        normalized_market = normalize_market_code(market)
        tick_data = tick_handler.get_real_tick_data(normalized_market, symbol)
        return tick_data

    except Exception as e:
        logger.error(f"[TDX Socket.IO] 获取 {market}:{symbol} tick 数据错误: {e}")
        return None

def get_enhanced_tick_data(market, symbol):
    """获取增强的tick数据，包含ExtParser需要的所有字段，并进行去重处理"""
    global tick_handler, last_tick_data, tick_data_lock

    if not tick_handler:
        logger.warning("[TDX Socket.IO] Tick数据处理器未初始化")
        return None

    try:
        # 标准化市场代码后获取基础tick数据
        normalized_market = normalize_market_code(market)
        base_tick = tick_handler.get_real_tick_data(normalized_market, symbol)
        if not base_tick:
            return None

        # 构建品种标识
        full_symbol = f"{market}:{symbol}"

        # 获取当前tick的关键信息用于去重比较
        current_timestamp = base_tick.get('timestamp', 0)
        current_price = base_tick.get('price', 0.0)
        current_volume = base_tick.get('volume', 0)
        current_time = base_tick.get('time', '')

        # 线程安全地检查是否为重复数据
        with tick_data_lock:
            # 检查是否有上一次的数据
            if full_symbol in last_tick_data:
                last_info = last_tick_data[full_symbol]

                # 比较时间戳和关键字段，判断是否为重复数据
                if (current_timestamp == last_info.get('timestamp', 0) and
                    current_time == last_info.get('time', '') and
                    current_price == last_info.get('price', 0.0) and
                    current_volume == last_info.get('volume', 0)):

                    logger.debug(f"[TDX Socket.IO] 跳过重复tick数据: {full_symbol} 时间={current_time} 价格={current_price}")
                    return None

            # 保存当前tick信息用于下次比较
            last_tick_data[full_symbol] = {
                'timestamp': current_timestamp,
                'time': current_time,
                'price': current_price,
                'volume': current_volume,
                'datetime': base_tick.get('datetime', ''),
                'update_time': time.time()  # 记录更新时间
            }

        # 转换为ExtParser兼容格式
        enhanced_tick = {
            # 基本标识
            'exchg': market.upper(),
            'code': symbol,
            'name': base_tick.get('name', ''),

            # 价格数据
            'price': base_tick.get('price', 0.0),
            'open': base_tick.get('open', 0.0),
            'high': base_tick.get('high', 0.0),
            'low': base_tick.get('low', 0.0),
            'last_close': base_tick.get('last_close', 0.0),

            # 成交数据
            'volume': base_tick.get('volume', 0),
            'amount': base_tick.get('amount', 0.0),

            # 买卖盘数据（五档）
            'ask_price_0': base_tick.get('ask1', 0.0),
            'ask_qty_0': base_tick.get('ask_vol1', 0),
            'bid_price_0': base_tick.get('bid1', 0.0),
            'bid_qty_0': base_tick.get('bid_vol1', 0),

            'ask_price_1': base_tick.get('ask2', 0.0),
            'ask_qty_1': base_tick.get('ask_vol2', 0),
            'bid_price_1': base_tick.get('bid2', 0.0),
            'bid_qty_1': base_tick.get('bid_vol2', 0),

            'ask_price_2': base_tick.get('ask3', 0.0),
            'ask_qty_2': base_tick.get('ask_vol3', 0),
            'bid_price_2': base_tick.get('bid3', 0.0),
            'bid_qty_2': base_tick.get('bid_vol3', 0),

            'ask_price_3': base_tick.get('ask4', 0.0),
            'ask_qty_3': base_tick.get('ask_vol4', 0),
            'bid_price_3': base_tick.get('bid4', 0.0),
            'bid_qty_3': base_tick.get('bid_vol4', 0),

            'ask_price_4': base_tick.get('ask5', 0.0),
            'ask_qty_4': base_tick.get('ask_vol5', 0),
            'bid_price_4': base_tick.get('bid5', 0.0),
            'bid_qty_4': base_tick.get('bid_vol5', 0),

            # 时间字段
            'timestamp': base_tick.get('timestamp', 0),
            'time': base_tick.get('time', ''),
            'date': base_tick.get('date', ''),
            'datetime': base_tick.get('datetime', ''),

            # 期货特有字段（如果有）
            'open_interest': base_tick.get('open_interest', 0),
            'diff_interest': base_tick.get('diff_interest', 0),
        }

        logger.debug(f"[TDX Socket.IO] 新tick数据: {full_symbol} 时间={current_time} 价格={current_price}")
        return enhanced_tick

    except Exception as e:
        logger.error(f"[TDX Socket.IO] 获取增强tick数据错误 {market}:{symbol}: {e}")
        return None

def cleanup_old_tick_data():
    """清理过期的tick数据缓存"""
    global last_tick_data, tick_data_lock

    current_time = time.time()
    cleanup_threshold = 300  # 5分钟过期时间

    with tick_data_lock:
        expired_symbols = []

        for symbol, tick_info in last_tick_data.items():
            update_time = tick_info.get('update_time', 0)
            if current_time - update_time > cleanup_threshold:
                expired_symbols.append(symbol)

        # 删除过期数据
        for symbol in expired_symbols:
            del last_tick_data[symbol]

        if expired_symbols:
            logger.debug(f"[TDX Socket.IO] 清理过期tick缓存: {len(expired_symbols)} 个品种")

def get_tick_cache_stats():
    """获取tick缓存统计信息"""
    global last_tick_data, tick_data_lock

    with tick_data_lock:
        total_symbols = len(last_tick_data)
        current_time = time.time()

        recent_count = 0
        for tick_info in last_tick_data.values():
            update_time = tick_info.get('update_time', 0)
            if current_time - update_time < 60:  # 1分钟内更新的
                recent_count += 1

        return {
            'total_cached_symbols': total_symbols,
            'recent_active_symbols': recent_count,
            'cache_memory_usage': f"{total_symbols * 200} bytes (估算)"  # 粗略估算
        }

# 添加 Flask 路由到 aiohttp 应用
async def flask_handler(request):
    """将 Flask 请求转发到 aiohttp"""
    # 这里可以添加 Flask 路由的处理逻辑
    # 或者保持 Flask 和 Socket.IO 分别运行
    return web.Response(text="TDX Socket.IO Server Running")

# 添加根路径处理
aio_app.router.add_get('/', flask_handler)

# 全局K线推送配置，默认值
KLINE_PUSH_CONFIG = {
    'include_unclosed': False,  # 是否包含未收盘K线
    'count': 300               # 推送K线条数
}

# K线订阅计数器，结构：{period: {symbol: count}}
kline_subscription_counters = defaultdict(lambda: defaultdict(int))

@app.route('/subscribe_kline', methods=['POST'])
def subscribe_kline():
    '''
    注册K线订阅，参数：period, symbol, include_unclosed, count, key
    '''
    if not request.is_json:
        logger.warning(f"[K线订阅] 错误: 请求体不是JSON, headers={dict(request.headers)}, body={request.data}")
        return jsonify({'error': '请求体必须为JSON'}), 400
    data = request.get_json()
    period = data.get('period')
    symbol = data.get('symbol')
    key = data.get('key', '')  # 新增key参数，默认为空字符串
    
    # period参数兼容 day/week/month
    period_map = {'day': '1D', 'week': '1W', 'month': '1M'}
    if period in period_map:
        logger.info(f"[K线订阅] period参数自动转换: {period} -> {period_map[period]}")
        period = period_map[period]
    
    # 构建key_symbol_period标识
    key_symbol_period = f"{key}_{symbol}_{period}"
    
    # 检查是否已经订阅过
    if key_symbol_period in key_subscription_list:
        logger.info(f"[K线订阅] 重复订阅，跳过: key={key}, symbol={symbol}, period={period}")
        return jsonify({'success': True, 'message': '已存在相同订阅，跳过操作'})
    
    # === 新增：写入Redis订阅集合 ===
    key_redis = f'kline_subs:{period}'
    redis_client.sadd(key_redis, symbol)
    include_unclosed = data.get('include_unclosed', None)
    count = data.get('count', None)
    if not period or not symbol:
        logger.warning(f"[K线订阅] 错误: 缺少period或symbol, data={data}")
        return jsonify({'error': 'period和symbol为必填参数'}), 400
    if BACKTEST_MODE and period != '1D':
        logger.warning(f"[K线订阅] 错误: 回测模式下周期不为1D, period={period}, data={data}")
        return jsonify({'error': '回测模式下只允许订阅日线（day）周期'}), 400
    # 回测模式下只允许day周期
    if BACKTEST_MODE and period != '1D':
        return jsonify({'error': '回测模式下只允许订阅日线（day）周期'}), 400
    # 计数器驱动逻辑
    current_count = kline_subscription_counters[period][symbol]
    if current_count == 0:
        logger.info(f'[K线订阅] 首次订阅，主动获取K线: period={period} symbol={symbol}')
        if ':' in symbol:
            market, code = symbol.split(':', 1)
        else:
            market, code = '', symbol
        _ = get_kline_with_cache(market, code, period, fetch_kline_data)
    kline_subscription_counters[period][symbol] += 1
    # 设置全局推送参数（只要有新订阅就更新）
    if include_unclosed is not None:
        KLINE_PUSH_CONFIG['include_unclosed'] = bool(include_unclosed)
    if count is not None:
        try:
            KLINE_PUSH_CONFIG['count'] = int(count)
        except Exception:
            pass
    
    # 添加到key_subscription_list
    key_subscription_list.add(key_symbol_period)
    
    # 保存持久化数据
    save_persistence_data()
    
    logger.info(f'[K线订阅][计数器] key={key} period={period} symbol={symbol} 当前计数={kline_subscription_counters[period][symbol]}')
    return jsonify({'success': True})

@app.route('/unsubscribe_kline', methods=['POST'])
def unsubscribe_kline():
    '''
    取消K线订阅，参数：period, symbol, key
    '''
    if not request.is_json:
        return jsonify({'error': '请求体必须为JSON'}), 400
    data = request.get_json()
    period = data.get('period')
    symbol = data.get('symbol')
    key = data.get('key', '')  # 新增key参数，默认为空字符串
    
    if not period or not symbol:
        return jsonify({'error': 'period和symbol为必填参数'}), 400
    
    # 构建key_symbol_period标识
    key_symbol_period = f"{key}_{symbol}_{period}"
    
    # 检查是否存在该订阅
    if key_symbol_period not in key_subscription_list:
        logger.warning(f"[K线订阅] 退订时未找到订阅记录: key={key}, symbol={symbol}, period={period}")
        return jsonify({'success': True, 'message': '未找到对应订阅记录，跳过操作'})
    
    # 计数器驱动逻辑
    current_count = kline_subscription_counters[period][symbol]
    if current_count > 0:
        kline_subscription_counters[period][symbol] -= 1
        logger.info(f'[K线订阅][计数器] 退订: key={key} period={period} symbol={symbol}，剩余计数={kline_subscription_counters[period][symbol]}')
        if kline_subscription_counters[period][symbol] == 0:
            key_redis = f'kline_subs:{period}'
            redis_client.srem(key_redis, symbol)
            logger.info(f'[K线订阅][计数器] 计数归零，已从Redis删除: period={period} symbol={symbol}')
            del kline_subscription_counters[period][symbol]
    else:
        logger.warning(f'[K线订阅][计数器] 退订时计数已为0: period={period} symbol={symbol}')
    
    # 从key_subscription_list中删除
    key_subscription_list.discard(key_symbol_period)
    
    # 保存持久化数据
    save_persistence_data()
    
    return jsonify({'success': True})

def get_period_seconds(period):
    mapping = {
        '1m': 60,
        '5m': 300,
        '15m': 900,
        '30m': 1800,
        '1h': 3600,
        '1D': 86400,
    }
    return mapping.get(period)

PERIODS = ['1m', '5m', '15m', '30m', '1h', '1D']

last_push_time = {p: 0 for p in PERIODS}

def is_new_period(period, now):
    sec = get_period_seconds(period)
    if not sec:
        return False
    # 健壮写法：判断是否跨周期，只要本周期还没推送过就推送
    return (now // sec) > (last_push_time[period] // sec)

def fetch_kline_data(market, code, period, count=800, start=0):
    import traceback
    logger.info(f"[调试日志] fetch_kline_data: market={market} code={code} period={period} count={count} start={start} 调用栈: {traceback.format_stack(limit=3)}")
    # --- 市场代码解析 (应用标准化) ---
    market_code = None
    try:
        market_int = int(market)
        if market_int >= 0:
            market_code = market_int
    except ValueError:
        normalized_market = normalize_market_code(market)
        market_code = MARKET_MAP.get(normalized_market)
    if market_code is None or code is None:
        return []
    # --- 周期代码解析 (区分 Hq 和 ExHq) ---
    period_info = PERIOD_MAP.get(period)
    if period_info is None:
        return []
    api_type = get_api_type(market_code)
    period_code = period_info.get(api_type)
    if period_code is None and api_type == 'ex':
        period_code = period_info.get('hq')
    if period_code is None:
        return []
    # --- 获取 API 实例 ---
    api = None
    if api_type == 'hq':
        logger.info(f"[调试日志] fetch_kline_data: get_tdx_hq_api即将被调用, market={market} code={code} period={period}")
        api = get_tdx_hq_api()
    elif api_type == 'ex':
        logger.info(f"[调试日志] fetch_kline_data: get_tdx_exhq_api即将被调用, market={market} code={code} period={period}")
        api = get_tdx_exhq_api()
    if not api:
        return []
    try:
        use_index_bars = False
        if market_code in [27, 47, 62]:
            use_index_bars = True
        elif market_code == 1:
            if code == '000001' or code.startswith(('000', '880', '999')):
                use_index_bars = True
        elif market_code == 0:
            if code.startswith('399'):
                use_index_bars = True
        if use_index_bars and api_type == 'hq':
            data = api.get_index_bars(period_code, market_code, code, start, count)
        else:
            if api_type == 'hq':
                data = api.get_security_bars(period_code, market_code, code, start, count)
            elif api_type == 'ex':
                data = api.get_instrument_bars(period_code, market_code, code, start, count)
        if not data:
            return []
        df = api.to_df(data)
        # 字段处理与 /kline 保持一致
        datetime_col_name = None
        if 'datetime' in df.columns:
            datetime_col_name = 'datetime'
        elif 'date' in df.columns:
            datetime_col_name = 'date'
        df['timestamp'] = None
        if datetime_col_name:
            dt_series = pd.to_datetime(df[datetime_col_name], errors='coerce')
            def to_shanghai_timestamp(dt):
                if pd.isnull(dt):
                    return None
                try:
                    if not isinstance(dt, (pd.Timestamp, datetime.datetime)):
                        dt = pd.to_datetime(dt)
                        if pd.isnull(dt): return None
                    if dt.tzinfo is None or dt.tzinfo.utcoffset(dt) is None:
                        dt_aware = SHANGHAI_TZ.localize(dt)
                    else:
                        dt_aware = dt.astimezone(SHANGHAI_TZ)
                    return int(dt_aware.timestamp())
                except Exception:
                    return None
            df['timestamp'] = dt_series.apply(to_shanghai_timestamp)
            def format_datetime_str(dt):
                if pd.isnull(dt):
                    return None
                try:
                    if not isinstance(dt, (pd.Timestamp, datetime.datetime)):
                        dt = pd.to_datetime(dt)
                        if pd.isnull(dt): return None
                    if datetime_col_name == 'datetime':
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        return dt.strftime('%Y-%m-%d')
                except Exception:
                    return None
            df[datetime_col_name] = dt_series.apply(format_datetime_str)
        # 成交量转手数
        if 'vol' in df.columns:
            df['vol'] = pd.to_numeric(df['vol'], errors='coerce').fillna(0)
            df['vol'] = df['vol'].apply(lambda x: int(x // 100) if pd.notnull(x) else 0)
        # 选择并重命名列
        required_columns = {
            'timestamp': 'time',
            'open': 'open',
            'high': 'high',
            'low': 'low',
            'close': 'close',
            'vol': 'volume',
        }
        if datetime_col_name and datetime_col_name in df.columns:
            required_columns[datetime_col_name] = datetime_col_name
        columns_to_select = {k: v for k, v in required_columns.items() if k in df.columns}
        df_output = df[list(columns_to_select.keys())].rename(columns=columns_to_select)
        df_output = df_output.where(pd.notnull(df_output), None)
        return df_output.to_dict('records')
    except Exception:
        return []
    finally:
        if api:
            disconnect_method = getattr(api, 'disconnect', None)
            if callable(disconnect_method):
                try:
                    disconnect_method()
                except Exception:
                    pass

# 工具函数：判断某K线时间是否已收盘，kline_time 是原始数据的最新一根k线，也就是包含了未收盘k线
def is_kline_closed(period, kline_time, now=None):
    """
    period: '1m', '5m', ...
    kline_time: 秒级时间戳（int）
    now: 当前时间戳（int），可选
    返回：True=已收盘，False=未收盘
    """
    if now is None:
        now = int(time.time())
    sec = get_period_seconds(period)
    if not sec:
        return True
    # 该K线的收盘时间 = kline_time + sec - 1
    close_time = kline_time + sec - 1
    return now > close_time

# 全局或类成员变量，初始化为True
trading_closed_map = {period: {} for period in PERIODS}  # {period: {symbol: bool}}

def timing_decision_func(market, code, period):
    """
    返回值：
    0 = 无动作
    1 = 仅重新获取K线
    2 = 重新获取K线且推送
    """
    import json
    import os
    import datetime
    # 1. 获取当前K线数据
    df = get_kline_with_cache(market, code, period, fetch_kline_data)
    if df.empty:
        return 1  # 没有K线，需拉取
    last_ktime = df.iloc[-1]['time'] if 'time' in df.columns else None
    if last_ktime is None:
        return 1
    # 2. 加载品类参数
    commodity_type = infer_commodity_type(market, code)
    commodity_json_path = os.path.join(os.path.dirname(__file__), 'strategy', 'commodity.json')
    if not os.path.exists(commodity_json_path):
        return 1
    with open(commodity_json_path, 'r', encoding='utf-8') as f:
        commodity_cfg = json.load(f)
    cfg = commodity_cfg.get(commodity_type, {})
    trading_days = cfg.get('trading_days', ["Mon", "Tue", "Wed", "Thu", "Fri"])
    open_time = cfg.get('open_time', '09:30')
    close_time = cfg.get('close_time', '15:00')
    timezone = cfg.get('timezone', 'Asia/Shanghai')
    now = datetime.datetime.now(pytz.timezone(timezone))
    in_trading = is_in_trading_time(now, trading_days, open_time, close_time, timezone)
    global trading_closed_map
    trading_closed = trading_closed_map[period].get(f'{market}:{code}', False)
    kline_closed = is_kline_closed(period, int(last_ktime))
    # 只在返回1或2时打印关键变量
    if in_trading:
        if trading_closed:
            logger.info(f"[timing_decision_func] return 1: in_trading={in_trading}, trading_closed={trading_closed}, is_kline_closed={kline_closed}, last_ktime={last_ktime}")
            trading_closed_map[period][f'{market}:{code}'] = False
            return 1
        if kline_closed:
            logger.info(f"[timing_decision_func] return 2: in_trading={in_trading}, trading_closed={trading_closed}, is_kline_closed={kline_closed}, last_ktime={last_ktime}")
            trading_closed_map[period][f'{market}:{code}'] = False
            return 2
        return 0
    else:
        if not trading_closed:
            logger.info(f"[timing_decision_func] return 2: in_trading={in_trading}, trading_closed={trading_closed}, is_kline_closed={kline_closed}, last_ktime={last_ktime}")
            trading_closed_map[period][f'{market}:{code}'] = True
            return 2
        return 0

def kline_push_worker():
    logger.info("[调试日志] kline_push_worker 线程启动")
    last_pushed_kline_time_map = {period: {} for period in PERIODS}
    try:
        while True:
            for period in PERIODS:
                key = f'kline_subs:{period}'
                symbols = redis_client.smembers(key)
                for symbol in symbols:
                    try:
                        if ':' in symbol:
                            market, code = symbol.split(':', 1)
                        else:
                            logger.warning(f'[K线推送] 订阅symbol格式不规范: {symbol}')
                            continue
                        # 调用时机函数
                        decision = timing_decision_func(market, code, period)
                        if decision == 0:
                            continue  # 无动作
                        # 重新获取一次K线数据
                        logger.info(f"[调试日志] timing_decision_func: period={period} symbol={symbol} decision={decision}")
                        logger.info(f"[调试日志] get_kline_with_cache调用: period={period} symbol={symbol} decision={decision}")
                        df = get_kline_with_cache(market, code, period, fetch_kline_data, True)
                        if df.empty:
                            continue
                        last_time = df.iloc[-1]['time'] if 'time' in df.columns else None
                        if decision == 2:
                            # 推送逻辑
                            include_unclosed = KLINE_PUSH_CONFIG.get('include_unclosed', False)
                            count = KLINE_PUSH_CONFIG.get('count', 300)
                            fetch_num = 1 if (include_unclosed and count == 1) else count + 1
                            df_to_push = df.tail(fetch_num)
                            if not df_to_push.empty:
                                data = df_to_push.tail(count).to_dict('records')
                            else:
                                data = []
                            channel = f'kline.{period}.{symbol}'
                            logger.info(f'[K线推送] 推送频道: {channel}，数据条数: {len(data)}')
                            redis_client.publish(channel, pyjson.dumps(data))
                            if last_time is not None:
                                last_pushed_kline_time_map[period][symbol] = int(last_time)
                    except Exception as e:
                        logger.error(f'[K线推送] {period} {symbol} 推送失败: {e}')
            time.sleep(1)
    except Exception as e:
        logger.error(f"[tdxserver][退出监控] kline_push_worker 线程异常退出: {e}", exc_info=True)
    finally:
        logger.error("[tdxserver][退出监控] kline_push_worker 线程已退出")

# 启动推送线程
# push_thread = threading.Thread(target=kline_push_worker, daemon=True)
# push_thread.start()

def get_kline_cache_id(market, code, period):
    '''生成唯一缓存ID，ETF等可根据code前缀或外部传入品种类型扩展''' 
    # 这里只做基础实现，后续如需区分etf/stock可扩展
    return f"{market}.{code}_{period}"

def get_kline_with_cache(market, code, period, fetch_func, force_fetch=False):
    """
    只在force_fetch为True或缓存不存在/数据为空时才拉取行情，否则只返回本地缓存。
    """
    import os
    import pandas as pd
    import traceback
    cache_id = get_kline_cache_id(market, code, period)
    h5file = f"kline_cache/{cache_id}.h5"
    lockfile = h5file + ".lock"
    os.makedirs(os.path.dirname(h5file), exist_ok=True)
    def to_df(data):
        return pd.DataFrame(data)
    # logger.info(f"[调试日志] get_kline_with_cache: market={market} code={code} period={period} h5file={h5file} force_fetch={force_fetch}")
    with FileLock(lockfile, timeout=30):
        # 1. 检查缓存
        if not os.path.exists(h5file):
            logger.info(f"[调试日志] get_kline_with_cache: 缓存不存在, 触发fetch, market={market} code={code} period={period}")
            data = fetch_func(market, code, period, count=800, start=0)
            logger.info(f"[调试日志] get_kline_with_cache: fetch_func返回, data_len={len(data) if hasattr(data, '__len__') else 'N/A'}")
            df = to_df(data)
            df.to_hdf(h5file, key='kline', mode='w')
            return df
        # 2. 有缓存
        df_old = pd.read_hdf(h5file, key='kline')
        if force_fetch or df_old.empty:
            logger.info(f"[调试日志] get_kline_with_cache: force_fetch或缓存为空, 触发fetch, market={market} code={code} period={period}")
            data = fetch_func(market, code, period, count=800, start=0)
            logger.info(f"[调试日志] get_kline_with_cache: fetch_func(force_fetch)返回, data_len={len(data) if hasattr(data, '__len__') else 'N/A'}")
            df = to_df(data)
            df.to_hdf(h5file, key='kline', mode='w')
            return df
        # 3. 只返回本地缓存
        # logger.info(f"[调试日志] get_kline_with_cache: 直接返回本地缓存, market={market} code={code} period={period}")
        return df_old

# 全局回测模式开关
BACKTEST_MODE = False

def backtest_kline_push_worker():
    logger.info("[tdxserver] backtest_kline_push_worker 线程启动")
    import datetime
    sim_date = datetime.date(2025, 4, 1)
    cache = {}
    max_date_map = {}  # 记录每个品种的最大K线日期
    try:
        while True:
            # 检查是否有任何订阅，没有则等待
            has_any_subscription = False
            for period in PERIODS:
                if period not in ['1D']:
                    continue
                key = f'kline_subs:{period}'
                symbols = redis_client.smembers(key)
                if symbols:
                    has_any_subscription = True
                    break
            if not has_any_subscription:
                time.sleep(10)
                continue
            # 有订阅后，推进模拟日期
            all_finished = True  # 标记所有品种是否都推送完
            for period in PERIODS:
                if period not in ['1D']:
                    continue  # 只允许day周期
                key = f'kline_subs:{period}'
                symbols = redis_client.smembers(key)
                for symbol in symbols:
                    try:
                        if ':' in symbol:
                            market, code = symbol.split(':', 1)
                        else:
                            logger.warning(f'[回测K线推送] 订阅symbol格式不规范: {symbol}')
                            continue
                        # 首次订阅拉取全历史日线
                        if symbol not in cache:
                            df = get_kline_with_cache(market, code, period, fetch_kline_data)
                            cache[symbol] = df
                            # 记录最大K线日期
                            if not df.empty and 'time' in df.columns:
                                max_time = df['time'].max()
                                # 转为日期
                                max_dt = datetime.datetime.fromtimestamp(max_time, SHANGHAI_TZ).date()
                                max_date_map[symbol] = max_dt
                            else:
                                max_date_map[symbol] = None
                            if not df.empty:
                                logger.info(f'[回测K线推送][DEBUG] {symbol} 背景K线最后10条: {df.tail(10).to_dict("records")}')
                        else:
                            df = cache[symbol]
                        # 只推送<=模拟日期的数据
                        if not df.empty and 'time' in df.columns:
                            sim_date_end_of_day = datetime.datetime.combine(sim_date, datetime.time.max)
                            sim_timestamp = int(SHANGHAI_TZ.localize(sim_date_end_of_day).timestamp())
                            df_visible = df[df['time'] <= sim_timestamp]
                            data = df_visible.to_dict('records')
                            # 判断是否还有数据可推送
                            max_dt = max_date_map.get(symbol)
                            if max_dt is not None and sim_date <= max_dt:
                                all_finished = False  # 只要有一个品种还没推送完就不退出
                        else:
                            data = []
                            # 没有数据也算推送完
                        channel = f'kline.{period}.{symbol}'
                        # 新增调试日志
                        logger.debug(f'[回测K线推送][DEBUG] 即将推送频道: {channel}, 数据条数: {len(data)}, 内容预览: {json.dumps(data[-3:], ensure_ascii=False)}')
                        redis_client.publish(channel, pyjson.dumps(data))
                        logger.info(f'[回测K线推送] {channel} 推送模拟日{sim_date}: {len(data)}条')
                    except Exception as e:
                        logger.error(f'[回测K线推送] {period} {symbol} 推送失败: {e}')
            # 检查是否所有品种都推送完毕
            if all_finished:
                logger.info(f'[回测K线推送] 所有品种历史K线已全部推送完毕，模拟日期 {sim_date}，线程自动退出。')
                break
            # 推进模拟日期，并跳过周末
            sim_date += datetime.timedelta(days=1)
            while sim_date.weekday() >= 5:  # Monday == 0, Sunday == 6
                sim_date += datetime.timedelta(days=1)
            time.sleep(10)
    except Exception as e:
        logger.error(f"[tdxserver][退出监控] backtest_kline_push_worker 线程异常退出: {e}", exc_info=True)
    finally:
        logger.error("[tdxserver][退出监控] backtest_kline_push_worker 线程已退出")

def infer_commodity_type(market: str, code: str) -> str:
    """
    简单区分A股股票和ETF，其他直接返回 'UNKNOWN'
    """
    if market in ('sh', 'sz'):
        etf_prefixes = ('5', '15', '51', '56', '58', '159', '510', '512', '513', '515', '518', '588')
        if code.startswith(etf_prefixes):
            return 'ETF'
        stock_prefixes = ('6', '0', '3')
        if code.startswith(stock_prefixes):
            return 'STOCK'
    return 'UNKNOWN'

def is_in_trading_time(now: datetime.datetime, trading_days: list, open_time: str, close_time: str, timezone: str) -> bool:
    """
    判断当前时间是否在指定的交易日和交易时间段内
    - now: 当前本地时间（datetime对象，建议用 datetime.now(pytz.UTC) 或指定时区）
    - trading_days: 允许的交易日（如 ["Mon", "Tue", ...]）
    - open_time: 开盘时间（如 "09:30"）
    - close_time: 收盘时间（如 "15:00"）
    - timezone: 时区字符串（如 "Asia/Shanghai"、"UTC"）
    """
    tz = pytz.timezone(timezone)
    now_local = now.astimezone(tz)
    weekday_str = now_local.strftime("%a")
    if weekday_str not in trading_days:
        return False
    open_hour, open_minute = map(int, open_time.split(":"))
    close_hour, close_minute = map(int, close_time.split(":"))
    open_dt = now_local.replace(hour=open_hour, minute=open_minute, second=0, microsecond=0)
    close_dt = now_local.replace(hour=close_hour, minute=close_minute, second=0, microsecond=0)
    return open_dt <= now_local <= close_dt

def should_fetch_and_push_kline(market, code, period, last_pushed_kline_time):
    """
    统一推送时机判断：
    - 判断是否有新K线（与last_pushed_kline_time对比）
    - 判断是否在交易时间（调用is_in_trading_time，需加载commodity.json配置）
    - 判断是否收盘（is_kline_closed）
    返回: True=需要推送，False=不需要
    """
    import json
    import os
    import datetime
    # 1. 获取K线数据
    df = get_kline_with_cache(market, code, period, fetch_kline_data)
    if df.empty:
        return False
    last_time = df.iloc[-1]['time'] if 'time' in df.columns else None
    if last_time is None:
        return False
    # 2. 判断是否有新K线
    if int(last_time) == int(last_pushed_kline_time):
        return False
    # 3. 加载品类参数
    commodity_type = infer_commodity_type(market, code)
    # 默认配置路径
    commodity_json_path = os.path.join(os.path.dirname(__file__), 'strategy', 'commodity.json')
    if not os.path.exists(commodity_json_path):
        return False
    with open(commodity_json_path, 'r', encoding='utf-8') as f:
        commodity_cfg = json.load(f)
    cfg = commodity_cfg.get(commodity_type, {})
    trading_days = cfg.get('trading_days', ["Mon", "Tue", "Wed", "Thu", "Fri"])
    open_time = cfg.get('open_time', '09:30')
    close_time = cfg.get('close_time', '15:00')
    timezone = cfg.get('timezone', 'Asia/Shanghai')
    # 4. 判断是否在交易时间
    now = datetime.datetime.now(pytz.timezone(timezone))
    if not is_in_trading_time(now, trading_days, open_time, close_time, timezone):
        return False
    # 5. 判断是否收盘
    if not is_kline_closed(period, int(last_time)):
        return False
    return True

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--backtest', action='store_true', help='启用回测模式')
    parser.add_argument('--clear-subs', action='store_true', help='启动时清除所有K线订阅')
    args = parser.parse_args()
    BACKTEST_MODE = args.backtest
    if BACKTEST_MODE:
        logger.info('【TDX服务】已启用回测模式')
    else:
        logger.info('【TDX服务】正常实盘模式')
    print("请确保通达信客户端 (tdxw.exe) 正在运行。")
    print("TDX 数据服务将同时启动 Flask HTTP 服务器和 Socket.IO 服务器")
    # --- 加载配置 (移除部分日志) ---
    loaded_config = load_server_config(CONFIG_FILE)
    redis_config = load_redis_config(REDIS_CONFIG_FILE)
    HQ_TDX_IP = loaded_config['hq']['ip']
    HQ_TDX_PORT = loaded_config['hq']['port']
    EXHQ_TDX_IP = loaded_config['exhq']['ip']
    EXHQ_TDX_PORT = loaded_config['exhq']['port']
    # --- 结束加载配置 ---

    # --- 初始化Tick数据处理器 ---
    tick_handler = TickDataHandler(
        market_map=MARKET_MAP,
        hq_connection_func=get_tdx_hq_api,
        exhq_connection_func=get_tdx_exhq_api
    )
    logger.info("Tick数据处理器初始化完成")
    # --- 结束初始化 ---

    # --- 执行动态检测 (修改日志) ---
    logger.info("正在检测通达信连接参数...")
    detected_servers = get_tdx_connection_info()
    config_updated = False

    # 检查 HQ 更新
    if detected_servers['hq']:
        detected_hq_ip = detected_servers['hq']['ip']
        detected_hq_port = detected_servers['hq']['port']
        if detected_hq_ip != HQ_TDX_IP or detected_hq_port != HQ_TDX_PORT:
            HQ_TDX_IP = detected_hq_ip
            HQ_TDX_PORT = detected_hq_port
            config_updated = True

    # 检查 ExHq 更新
    if detected_servers['exhq']:
        detected_exhq_ip = detected_servers['exhq']['ip']
        detected_exhq_port = detected_servers['exhq']['port']
        if detected_exhq_ip != EXHQ_TDX_IP or detected_exhq_port != EXHQ_TDX_PORT:
            EXHQ_TDX_IP = detected_exhq_ip
            EXHQ_TDX_PORT = detected_exhq_port
            config_updated = True

    # --- 保存更新后的配置 (如果发生变化) ---
    if config_updated:
        logger.info(f"检测到新的服务器地址，正在更新配置文件 '{CONFIG_FILE}'...")
        current_config = {
            'hq': {'ip': HQ_TDX_IP, 'port': HQ_TDX_PORT},
            'exhq': {'ip': EXHQ_TDX_IP, 'port': EXHQ_TDX_PORT}
        }
        save_server_config(CONFIG_FILE, current_config)
    # --- 结束保存配置 ---

    # --- 打印最终检测结果和启动信息 (修改日志) ---
    hq_status = f"检测到普通行情接口: {HQ_TDX_IP}:{HQ_TDX_PORT}" if detected_servers['hq'] else "未检测到普通行情接口。"
    exhq_status = f"检测到扩展行情接口: {EXHQ_TDX_IP}:{EXHQ_TDX_PORT}" if detected_servers['exhq'] else "未检测到扩展行情接口。"
    logger.info(hq_status)
    logger.info(exhq_status)
    # --------------------------------

    # --- 加载持久化数据 ---
    logger.info("正在加载持久化数据...")
    load_persistence_data()
    logger.info("持久化数据加载完成")
    # --------------------------------

    # --- 处理 --clear-subs 参数 ---
    if args.clear_subs:
        logger.info('[启动参数] 检测到 --clear-subs 参数，开始清理所有订阅数据...')
        
        # 清空Redis订阅集合
        for period in PERIODS:
            key = f'kline_subs:{period}'
            redis_client.delete(key)
            logger.info(f'[启动参数] 已清空Redis订阅集合: {key}')
        
        # 记录清理前的数量
        key_list_count = len(key_subscription_list)
        sub_counters_count = len(subscription_counters)
        kline_counters_count = len(kline_subscription_counters)
        
        # 清空key_subscription_list
        key_subscription_list.clear()
        logger.info(f'[启动参数] 已清空key_subscription_list，原有 {key_list_count} 项')
        
        # 清空subscription_counters
        subscription_counters.clear()
        logger.info(f'[启动参数] 已清空subscription_counters，原有 {sub_counters_count} 项')
        
        # 清空kline_subscription_counters
        kline_subscription_counters.clear()
        logger.info(f'[启动参数] 已清空kline_subscription_counters，原有 {kline_counters_count} 个周期')
        
        # 保存清空后的状态到持久化文件
        save_persistence_data()
        logger.info(f'[启动参数] 已更新持久化文件，所有订阅数据已清空')
    # --------------------------------

    # === 主流程：只允许启动一个推送线程 ===
    if BACKTEST_MODE:
        push_thread = threading.Thread(target=backtest_kline_push_worker, daemon=True)
        push_thread.start()
    else:
        push_thread = threading.Thread(target=kline_push_worker, daemon=True)
        push_thread.start()
    # ...启动 Flask/SocketIO 服务器等...

    # 启动服务器
    def run_flask_server():
        """在单独线程中运行 Flask 服务器"""
        try:
            logger.info("[tdxserver][退出监控] [TDX 数据服务] 启动 Flask HTTP 服务器在端口 5003...")
            app.run(host='127.0.0.1', port=5003, debug=False, use_reloader=False)
        except Exception as e:
            logger.error(f"[tdxserver][退出监控] Flask 服务器异常退出: {e}", exc_info=True)
        finally:
            logger.error("[tdxserver][退出监控] Flask 服务器线程已退出")

    def run_socketio_server():
        """运行 Socket.IO 服务器"""
        try:
            logger.info("[tdxserver][退出监控] [TDX 数据服务] 启动 Socket.IO 服务器在端口 5004...")
            web.run_app(aio_app, host='127.0.0.1', port=5004)
        except Exception as e:
            logger.error(f"[tdxserver][退出监控] Socket.IO 服务器异常退出: {e}", exc_info=True)
        finally:
            logger.error("[tdxserver][退出监控] Socket.IO 服务器主线程已退出")

    # 在单独线程中启动 Flask 服务器
    flask_thread = threading.Thread(target=run_flask_server, daemon=True)
    flask_thread.start()

    logger.info("TDX 数据服务启动完成:")
    logger.info("  - HTTP API 服务: http://127.0.0.1:5003")
    logger.info("  - Socket.IO 服务: http://127.0.0.1:5004")
    logger.info("  - Socket.IO 连接路径: /socket.io")

    # 运行 Socket.IO 服务器（主线程）
    run_socketio_server()

    redis_config = load_redis_config(REDIS_CONFIG_FILE)

    # 注册主进程退出钩子
    atexit.register(lambda: logger.error("[tdxserver][退出监控] 主进程即将退出 (atexit)"))

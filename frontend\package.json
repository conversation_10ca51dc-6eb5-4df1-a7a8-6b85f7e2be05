{"name": "QuantQuart-frontend", "version": "1.0.0", "private": true, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.8.6", "@ant-design/pro-layout": "^7.22.0", "@ant-design/pro-provider": "^2.15.3", "@antv/g2": "^5.3.0", "@antv/g2plot": "^2.4.33", "@debut/indicators": "^1.3.22", "@types/chai": "^5.2.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/mocha": "^10.0.10", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "antd": "^5.24.3", "axios": "^1.7.9", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "jest": "^29.7.0", "jotai": "^2.11.0", "js-yaml": "^4.1.0", "klinecharts": "^10.0.0-alpha5", "mitt": "^3.0.1", "msw": "^2.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-json-view": "^1.21.3", "react-router-dom": "^7.1.1", "react-split": "^2.0.14", "rxjs": "^7.8.1", "socket.io-client": "^4.8.1", "trading-signals": "^6.0.1", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "uuid": "^11.0.5", "vite": "^6.2.1"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@craco/craco": "^7.x", "@rollup/plugin-babel": "^6.0.4", "@types/node": "^22.13.10", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "craco-less": "^2.x", "less": "^4.2.2", "less-loader": "^12.2.0", "less-plugin-npm-import": "^2.1.0", "react-scripts": "5.0.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2", "wait-on": "^8.0.3", "webpack-bundle-analyzer": "^4.10.2"}, "type": "module", "scripts": {"dev": "wait-on http://localhost:3000/api/health && vite", "build": "vite build", "serve": "vite preview", "start": "craco start", "test": "craco test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
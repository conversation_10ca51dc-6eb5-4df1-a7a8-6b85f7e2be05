#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
easytrader_ths客户端主程序

该程序在Windows端运行，负责启动同花顺交易接口和反向连接服务。
通过内网穿透机制，使远程后端能够与本地同花顺客户端通信。
"""

# pyright: reportGeneralTypeIssues=false
# 忽略类型检查错误，因为 easytrader 库没有提供类型注解

import os
import sys
import time
import json
import random
import logging
import argparse
import requests
import threading
import getpass
# --- 替换为 jqktrader 实现 ---
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "jqktrader"))
from jqktrader.clienttrader import ClientTrader
import codecs
try:
    import socketio
except AttributeError:
    # 尝试从 python-socketio 导入
    from socketio import Client as SocketIOClient
else:
    SocketIOClient = socketio.Client
from flask import Flask, request, jsonify
import csv
from datetime import datetime
import uuid
# 任务csv文件名
ORDERS_CSV = "orders.csv"
ORDERS_CSV_FIELDS = ["id", "created_at", "action", "params", "status", "executed_at", "result"]
import pytz
from datetime import datetime, time as dtime
import json as _json

ORDERS_POLL_INTERVAL = 10  # 轮询间隔秒

# 加载品类配置
with open(os.path.join(os.path.dirname(__file__), 'commodity.json'), 'r', encoding='utf-8') as f:
    commodity_config = _json.load(f)

def is_in_trading_time(symbol, commodity_config):
    # symbol: 'SSE.ETF.518880'，中间字段为品类
    parts = symbol.split('.')
    if len(parts) < 2:
        return False
    category = parts[1].upper()
    info = commodity_config.get(category)
    if not info:
        return False
    tz = pytz.timezone(info['timezone'])
    now = datetime.now(tz)
    open_t = dtime.fromisoformat(info['open_time'])
    close_t = dtime.fromisoformat(info['close_time'])
    weekday = now.strftime('%a')
    if weekday not in info['trading_days']:
        return False
    if open_t <= now.time() <= close_t:
        return True
    return False

def extract_symbol_from_params(params):
    # params为json字符串或dict
    if isinstance(params, str):
        try:
            params = _json.loads(params)
        except Exception:
            return None
    return params.get('code') or params.get('symbol')


os.environ['PYTHONIOENCODING'] = 'utf-8'
sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# 配置日志
# --- 修改：明确指定 FileHandler 的编码 ---
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_file_handler = logging.FileHandler("easytrader_ths_client.log", encoding='utf-8') # 指定编码
log_file_handler.setFormatter(log_formatter)
log_stream_handler = logging.StreamHandler()
log_stream_handler.setFormatter(log_formatter)

logger = logging.getLogger("EasyTrader-THS-Client")
logger.setLevel(logging.INFO)
logger.addHandler(log_file_handler)
logger.addHandler(log_stream_handler)
# 移除旧的 basicConfig 调用
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler("easytrader_ths_client.log"),
#         logging.StreamHandler()
#     ]
# )
# --- 结束修改 ---

# 全局变量
trader = None
app = Flask(__name__)
sio = socketio.Client(logger=True, engineio_logger=True)  # type: ignore # 忽略 Client 类型检查
socket_connected = False
user_password = None  # 新增：全局变量，仅保存在内存中

config = {
    "username": "",
    "backend_url": "",
    "local_port": 8888,
    "heartbeat_interval": 60,
    "ths_path": "",
    "socket_port": None,   # Socket.IO端口
}

# 认证token只在内存中保存，不写入配置文件
auth_token = ""

def init_trader():
    """初始化同花顺交易接口（使用 jqktrader）"""
    global trader
    try:
        # 检查并补全 ths_path
        ths_path = config['ths_path']
        if not ths_path.lower().endswith('.exe'):
            ths_path = ths_path.rstrip('\\/ ')
            ths_path = ths_path + ("/" if "/" in ths_path else "\\") + "xiadan.exe"
        # 检查并补全 tesseract_cmd
        tesseract_cmd = config.get('tesseract_cmd', '')
        if tesseract_cmd and not tesseract_cmd.lower().endswith('.exe'):
            tesseract_cmd = tesseract_cmd.rstrip('\\/ ')
            tesseract_cmd = tesseract_cmd + ("/" if "/" in tesseract_cmd else "\\") + "tesseract.exe"
        logger.info(f"[jqktrader] 正在初始化同花顺交易接口，路径: {ths_path}，OCR: {tesseract_cmd}")
        trader = ClientTrader()
        trader.connect(
            exe_path=ths_path,
            tesseract_cmd=tesseract_cmd
        )
        logger.info("[jqktrader] 同花顺交易接口初始化成功")
        # 检查登录状态
        account_info = trader.balance
        logger.info(f"[jqktrader] 账户信息: {account_info}")
        return True
    except Exception as e:
        logger.error(f"[jqktrader] 初始化同花顺交易接口失败: {str(e)}")
        return False

def get_actual_position(code):
    """查询当前实际持仓数量，返回int，若无则为0"""
    try:
        if trader is None:
            return 0
        positions = trader.position
        for pos in positions:
            if str(pos.get('证券代码')) == str(code):
                return int(float(pos.get('可用余额', 0)))
        return 0
    except Exception as e:
        logger.error(f"[持仓查询] 获取{code}持仓失败: {e}")
        return 0

# Socket.IO事件处理
@sio.event
def connect():
    """Socket.IO默认命名空间连接成功事件"""
    global socket_connected
    logger.info("Socket.IO默认命名空间连接成功")
    # 不设置 socket_connected = True，因为我们需要等待 /trade 命名空间连接成功

@sio.on('connect', namespace='/trade')
def connect_trade():
    """Socket.IO /trade 命名空间连接成功事件"""
    global socket_connected
    logger.info("Socket.IO /trade 命名空间连接成功")
    socket_connected = True

    # 连接成功后立即注册
    register_via_socketio()

@sio.event
def connect_error(error):
    """Socket.IO默认命名空间连接错误事件"""
    logger.error(f"Socket.IO默认命名空间连接错误: {error}")

@sio.on('connect_error', namespace='/trade')
def connect_error_trade(error):
    """Socket.IO /trade 命名空间连接错误事件"""
    global socket_connected
    logger.error(f"Socket.IO /trade 命名空间连接错误: {error}")
    socket_connected = False

@sio.event
def disconnect():
    """Socket.IO默认命名空间断开连接事件"""
    logger.info("Socket.IO默认命名空间断开连接")

@sio.on('disconnect', namespace='/trade')
def disconnect_trade():
    """Socket.IO /trade 命名空间断开连接事件"""
    global socket_connected
    logger.info("Socket.IO /trade 命名空间断开连接")
    socket_connected = False

    # 尝试重新连接
    threading.Timer(5.0, connect_to_socketio).start()

# 修改socket和Flask接口指令接收部分为写入csv，不直接执行
# 1. Socket.IO事件 on_execute_command/on_command
@sio.on('execute_command', namespace='/trade')
def on_execute_command(data):
    logger.info(f"收到执行命令: {data}")
    try:
        action = data.get('action')
        params = data.get('params', {})
        if not action:
            logger.error("执行命令失败: 缺少action参数")
            sio.emit('command_response', {
                'success': False,
                'message': '缺少action参数'
            }, namespace='/trade')
            return
        order_id = append_order_to_csv(action, params)
        sio.emit('command_response', {
            'success': True,
            'action': action,
            'order_id': order_id
        }, namespace='/trade')
    except Exception as e:
        logger.error(f"执行命令失败: {str(e)}")
        sio.emit('command_response', {
            'success': False,
            'message': str(e)
        }, namespace='/trade')

@sio.on('check_health', namespace='/trade')
def on_check_health(data):
    """处理健康检查请求"""
    logger.info(f"收到健康检查请求: {data}")

    try:
        # 获取健康状态数据
        health_data = get_health_data()

        # 发送健康状态响应
        sio.emit('health_response', {
                'success': True,
            'data': health_data
            }, namespace='/trade')
        
        logger.info("已发送健康状态响应")
    except Exception as e:
        logger.error(f"处理健康检查请求失败: {str(e)}")
        sio.emit('health_response', {
            'success': False,
            'message': str(e)
        }, namespace='/trade')

@sio.on('health_test', namespace='/trade')
def on_health_test(data):
    """处理简单的健康测试请求 - 直接返回true表示正在运行"""
    logger.info(f"收到健康测试请求: {data}")
    
    try:
        # 直接返回true表示客户端正在运行并能回应
        response_data = {
            'success': True,
            'status': 'running',
            'timestamp': int(time.time()),
            'client_id': f"{config['username']}_easytrader_ths_client",
            'message': 'Client is running and responding'
        }
        
        # 发送健康测试响应
        sio.emit('health_test_response', response_data, namespace='/trade')
        
        logger.info("已发送健康测试响应")
        return True
    except Exception as e:
        logger.error(f"处理健康测试请求失败: {str(e)}")
        sio.emit('health_test_response', {
            'success': False,
            'status': 'error',
            'message': str(e),
            'timestamp': int(time.time())
        }, namespace='/trade')
        return False

# 新增：兼容copy_client的指令接收方式，监听command事件
@sio.on('command', namespace='/trade')
def on_command(data):
    logger.info(f"[交易客户端] 收到后端命令: {data}")
    try:
        action = data.get('action')
        params = data.get('params', {})
        order_id = append_order_to_csv(action, params)
        sio.emit('response', {
            'success': True,
            'action': action,
            'order_id': order_id
        }, namespace='/trade')
    except Exception as e:
        logger.error(f"执行命令失败: {str(e)}")
        sio.emit('response', {
            'success': False,
            'action': data.get('action', 'unknown'),
            'message': str(e)
        }, namespace='/trade')

def get_health_data():
    """获取健康状态数据"""
    try:
        import psutil
        import platform
        from datetime import datetime

        # 基本状态信息
        health_data = {
            "status": "healthy",  # 默认为健康状态
            "timestamp": int(time.time()),
            "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "uptime": int(time.time() - psutil.boot_time()),
            "client_info": {
                "username": config["username"],
                "local_ip": get_local_ip(),
                "local_port": config["local_port"],
                "backend_url": config["backend_url"],
                "version": "1.0.0"  # 客户端版本
            },
            "system_info": {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "cpu_count": psutil.cpu_count(),
                "memory_total": psutil.virtual_memory().total,
                "memory_available": psutil.virtual_memory().available
            },
            "resource_usage": {
                "cpu_percent": psutil.cpu_percent(interval=0.1),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent
            }
        }

        # 交易接口状态
        trader_status = {
            "initialized": trader is not None,
            "status": "unknown"
        }

        if trader is not None:
            try:
                # 尝试获取账户信息，检查交易接口是否正常
                balance = trader.balance
                trader_status["status"] = "connected"
                trader_status["last_balance_check"] = int(time.time())
                trader_status["balance_check_result"] = "success"

                # 尝试获取持仓信息
                try:
                    position = trader.position
                    trader_status["position_check"] = "success"
                except Exception as pos_e:
                    trader_status["position_check"] = "failed"
                    trader_status["position_error"] = str(pos_e)
            except Exception as e:
                trader_status["status"] = "error"
                trader_status["error"] = str(e)
                health_data["status"] = "warning"  # 交易接口异常，健康状态降级为警告
        else:
            trader_status["status"] = "not_initialized"
            health_data["status"] = "warning"  # 交易接口未初始化，健康状态降级为警告

        health_data["trader"] = trader_status

        # Socket.IO连接状态
        socketio_status = {
            "connected": socket_connected,
            "port": config.get("socket_port"),
            "last_heartbeat": None  # 可以在心跳线程中记录最后一次心跳时间
        }

        if not socket_connected:
            health_data["status"] = "warning"  # Socket.IO未连接，健康状态降级为警告

        health_data["socketio"] = socketio_status

        # 检查是否有严重问题
        critical_issues = []

        # 检查CPU使用率是否过高
        if health_data["resource_usage"]["cpu_percent"] > 90:
            critical_issues.append("CPU使用率过高")

        # 检查内存使用率是否过高
        if health_data["resource_usage"]["memory_percent"] > 90:
            critical_issues.append("内存使用率过高")

        # 检查磁盘使用率是否过高
        if health_data["resource_usage"]["disk_usage"] > 90:
            critical_issues.append("磁盘使用率过高")

        # 如果交易接口状态为error，添加到严重问题列表
        if trader_status["status"] == "error":
            critical_issues.append(f"交易接口异常: {trader_status.get('error', '未知错误')}")

        # 如果有严重问题，健康状态降级为不健康
        if critical_issues:
            health_data["status"] = "unhealthy"
            health_data["critical_issues"] = critical_issues

        return health_data
    except Exception as e:
        logger.error(f"获取健康状态数据失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": int(time.time())
        }

def execute_command(action, params):
    """执行命令"""
    if trader is None:
        raise Exception("交易接口未初始化")

    if action == 'buy':
        code = params.get('code')
        price = params.get('price')
        amount = params.get('amount')

        if not all([code, price is not None, amount is not None]):
            raise Exception("参数不完整 (code, price, amount)")

        return trader.buy(str(code), price=float(price), amount=int(amount))  # type: ignore

    elif action == 'sell':
        code = params.get('code')
        price = params.get('price')
        amount = params.get('amount')

        if not all([code, price is not None, amount is not None]):
            raise Exception("参数不完整 (code, price, amount)")

        # 查询当前实际可卖持仓
        actual_position = get_actual_position(code)
        sell_qty = min(int(amount), actual_position)
        if sell_qty > 0:
            result = trader.sell(str(code), float(price), sell_qty)
            if sell_qty < int(amount):
                logger.warning(f"[卖出指令] 指令数量{amount}大于实际可卖持仓{actual_position}，仅卖出{sell_qty}")
                result = {"partial_filled": True, "executed_qty": sell_qty, "requested_qty": amount, "result": result}
            return result
        else:
            logger.warning(f"[卖出指令] 无可卖持仓，指令数量{amount}，实际持仓{actual_position}")
            return {"error": "无可卖持仓", "executed_qty": 0, "requested_qty": amount}

    elif action == 'cancel':
        entrust_no = params.get('entrust_no')

        if not entrust_no:
            raise Exception("参数不完整 (entrust_no)")

        return trader.cancel_entrust(int(entrust_no))

    elif action == 'balance':
        return trader.balance

    elif action == 'position':
        return trader.position

    elif action == 'today_entrusts':
        return trader.today_entrusts

    elif action == 'today_trades':
        return trader.today_trades

    elif action == 'refresh':
        trader.refresh()
        return {"message": "刷新成功"}

    else:
        raise Exception(f"不支持的操作: {action}")

def register_via_socketio():
    """通过Socket.IO注册客户端"""
    try:
        # 使用固定的客户端ID格式：username_easytrader_ths_client
        client_id = f"{config['username']}_easytrader_ths_client"

        data = {
            "username": config["username"],
            "client_type": "easytrader_ths",
            "client_id": client_id,
            "timestamp": int(time.time())
        }

        logger.info(f"通过Socket.IO注册客户端，使用ID: {client_id}")
        # 在 /trade 命名空间中发送注册消息
        sio.emit('register', data, namespace='/trade')
        return True
    except Exception as e:
        logger.error(f"通过Socket.IO注册客户端失败: {str(e)}")
        return False

def get_socket_port():
    """从服务器获取可用的Socket.IO端口"""
    global config, auth_token

    # 确保已登录并获取token
    if not auth_token and not login_to_backend():
        logger.error("未能获取认证token，无法获取Socket.IO端口")
        return False

    try:
        # 构建获取端口API URL
        # 检查 backend_url 是否已经包含 /api/trade 路径
        backend_url = config['backend_url']
        # 移除末尾的斜杠（如果有）
        if backend_url.endswith('/'):
            backend_url = backend_url[:-1]

        # 如果 backend_url 已经包含 /api/trade，则直接使用基础 URL
        if '/api/trade' in backend_url:
            # 提取基础 URL（去掉 /api/trade 部分）
            base_url = backend_url.split('/api/trade')[0]
            port_url = f"{base_url}/api/socket/get_port"
        else:
            # 否则直接添加 /api/socket/get_port
            port_url = f"{backend_url}/api/socket/get_port"

        if not port_url.startswith(("http://", "https://")):
            port_url = f"http://{port_url}"

        logger.info(f"尝试获取Socket.IO端口: {port_url}")

        # 添加认证头
        headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }

        # 发送请求
        response = requests.post(
            port_url,
            headers=headers,
            timeout=10
        )

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            # 打印响应内容以便调试
            logger.info(f"获取Socket.IO端口响应: {result}")

            # 检查响应格式，支持两种可能的格式：
            # 1. { success: true, port: 8000 }
            # 2. { success: true, data: { port: 8000 } }
            if result.get("success"):
                # 检查 port 是在根级别还是在 data 对象中
                if result.get("port"):
                    config["socket_port"] = result["port"]
                    logger.info(f"成功获取Socket.IO端口: {config['socket_port']}")
                    save_config()
                    return True
                elif result.get("data") and result["data"].get("port"):
                    config["socket_port"] = result["data"]["port"]
                    logger.info(f"成功获取Socket.IO端口: {config['socket_port']}")
                    save_config()
                    return True
                else:
                    logger.error("获取Socket.IO端口响应中未找到port字段")
                    print("获取Socket.IO端口响应中未找到port字段")
            else:
                error_msg = result.get('error') or result.get('message') or '未知错误'
                logger.error(f"获取Socket.IO端口失败: {error_msg}")
                print(f"获取Socket.IO端口失败: {error_msg}")
        elif response.status_code in [401, 403]:
            logger.error(f"认证失败，token可能已过期，状态码: {response.status_code}")
            print(f"认证失败，token可能已过期，状态码: {response.status_code}")
            # 清除token并尝试重新登录
            auth_token = ""
            if login_to_backend():
                logger.info("重新登录成功，尝试重新获取Socket.IO端口")
                # 重新尝试获取端口
                return get_socket_port()
            else:
                logger.error("重新登录失败，无法获取Socket.IO端口")
                print("重新登录失败，无法获取Socket.IO端口")
        else:
            logger.error(f"获取Socket.IO端口失败，状态码: {response.status_code}")
            print(f"获取Socket.IO端口失败，状态码: {response.status_code}")

        return False
    except Exception as e:
        logger.error(f"获取Socket.IO端口时发生错误: {str(e)}")
        print(f"获取Socket.IO端口时发生错误: {str(e)}")
        return False

def release_socket_port():
    """释放Socket.IO端口"""
    global config, auth_token

    if not config.get("socket_port"):
        logger.debug("没有Socket.IO端口需要释放")
        return True

    # 确保已登录并获取token
    if not auth_token and not login_to_backend():
        logger.error("未能获取认证token，无法释放Socket.IO端口")
        return False

    try:
        # 构建释放端口API URL
        # 检查 backend_url 是否已经包含 /api/trade 路径
        backend_url = config['backend_url']
        # 移除末尾的斜杠（如果有）
        if backend_url.endswith('/'):
            backend_url = backend_url[:-1]

        # 如果 backend_url 已经包含 /api/trade，则直接使用基础 URL
        if '/api/trade' in backend_url:
            # 提取基础 URL（去掉 /api/trade 部分）
            base_url = backend_url.split('/api/trade')[0]
            release_url = f"{base_url}/api/socket/release_port"
        else:
            # 否则直接添加 /api/socket/release_port
            release_url = f"{backend_url}/api/socket/release_port"

        if not release_url.startswith(("http://", "https://")):
            release_url = f"http://{release_url}"

        logger.info(f"尝试释放Socket.IO端口 {config['socket_port']}: {release_url}")

        # 添加认证头
        headers = {
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }

        # 发送请求
        response = requests.post(
            release_url,
            json={"port": config["socket_port"]},
            headers=headers,
            timeout=10
        )

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                logger.info(f"成功释放Socket.IO端口: {config['socket_port']}")
                config["socket_port"] = None
                save_config()
                return True
            else:
                logger.error(f"释放Socket.IO端口失败: {result.get('message', '未知错误')}")
        elif response.status_code in [401, 403]:
            logger.error(f"认证失败，token可能已过期，状态码: {response.status_code}")
            print(f"认证失败，token可能已过期，状态码: {response.status_code}")
            # 清除token并尝试重新登录
            auth_token = ""
            if login_to_backend():
                logger.info("重新登录成功，尝试重新释放Socket.IO端口")
                # 重新尝试释放端口
                return release_socket_port()
            else:
                logger.error("重新登录失败，无法释放Socket.IO端口")
                print("重新登录失败，无法释放Socket.IO端口")
        else:
            logger.error(f"释放Socket.IO端口失败，状态码: {response.status_code}")
            print(f"释放Socket.IO端口失败，状态码: {response.status_code}")

        return False
    except Exception as e:
        logger.error(f"释放Socket.IO端口时发生错误: {str(e)}")
        return False

def connect_to_socketio():
    """连接到Socket.IO服务器"""
    global socket_connected, config

    if socket_connected:
        logger.debug("Socket.IO已连接，无需重新连接")
        return True

    # 确保已登录并获取token
    global auth_token
    if not auth_token and not login_to_backend():
        logger.error("未能获取认证token，无法连接Socket.IO")
        return False

    # 如果之前的端口连接失败，尝试重新获取端口
    if config.get("socket_port") and not socket_connected:
        logger.info(f"之前的端口 {config['socket_port']} 连接失败，尝试释放并重新获取端口")
        try:
            # 尝试释放当前端口
            release_socket_port()
            # 清除端口配置
            config["socket_port"] = None
        except Exception as e:
            logger.warning(f"释放端口时出错，将直接尝试获取新端口: {e}")

    # 确保已获取Socket.IO端口
    if not config.get("socket_port") and not get_socket_port():
        logger.error("未能获取Socket.IO端口，无法连接Socket.IO")
        # 等待一段时间后再次尝试
        logger.info("等待5秒后再次尝试获取端口...")
        time.sleep(5)
        if not get_socket_port():
            logger.error("再次尝试获取端口失败")
        return False

    try:
        # 从backend_url中提取主机名
        backend_url = config["backend_url"]

        # 移除协议前缀
        if backend_url.startswith(("http://", "https://")):
            backend_url = backend_url.replace("http://", "").replace("https://", "")

        # 移除路径部分，只保留主机名
        if "/" in backend_url:
            backend_url = backend_url.split("/")[0]

        # 提取主机名（不包含端口）
        host = backend_url.split(':')[0]

        # 构建Socket.IO连接URL
        socketio_url = f"http://{host}:{config['socket_port']}"
        logger.info(f"正在连接Socket.IO服务器: {socketio_url}")

        # 检查端口是否可访问
        try:
            logger.info(f"检查端口 {config['socket_port']} 是否可访问...")
            response = requests.get(f"http://{host}:{config['socket_port']}", timeout=3)
            logger.info(f"端口检查响应: {response.status_code}")
        except Exception as e:
            logger.warning(f"端口 {config['socket_port']} 检查失败: {e}")
            # 端口不可访问，尝试重新获取
            logger.info("尝试重新获取端口...")
            config["socket_port"] = None
            if not get_socket_port():
                logger.error("重新获取端口失败")
                return False
            # 更新连接URL
            socketio_url = f"http://{host}:{config['socket_port']}"
            logger.info(f"使用新端口连接Socket.IO服务器: {socketio_url}")

        # 连接到Socket.IO服务器
        logger.info("开始连接Socket.IO服务器...")
        connection_success = False

        # 尝试不同的传输方式
        transport_options = [
            ["websocket", "polling"],  # 优先WebSocket
            ["polling", "websocket"],  # 优先Polling
            ["polling"],               # 仅Polling
            ["websocket"]              # 仅WebSocket
        ]

        for transports in transport_options:
            if connection_success:
                break

            transport_str = ", ".join(transports)
            logger.info(f"尝试使用传输方式 [{transport_str}] 连接...")

            try:
                # 如果已连接，先断开
                if sio.connected:
                    try:
                        sio.disconnect()
                        logger.info("断开现有连接")
                        time.sleep(1)  # 等待连接完全关闭
                    except Exception as disc_error:
                        logger.warning(f"断开连接时出错: {disc_error}")

                # 尝试连接
                sio.connect(
                    socketio_url,
                    socketio_path="/socket.io",
                    namespaces=["/trade"],
                    wait=True,
                    wait_timeout=5,
                    transports=transports
                )
                logger.info(f"使用传输方式 [{transport_str}] 连接初始化成功")
                connection_success = True
            except Exception as connect_error:
                logger.warning(f"使用传输方式 [{transport_str}] 连接失败: {connect_error}")
                time.sleep(1)  # 短暂等待后尝试下一种方式

        if not connection_success:
            logger.error("所有传输方式都连接失败")
            return False

        # 等待连接建立
        logger.info("等待Socket.IO连接完全建立...")
        for i in range(20):  # 最多等待20秒
            if socket_connected:
                logger.info(f"Socket.IO连接成功，用时 {i+1} 秒")
                # 连接成功后，主动发送一个心跳包
                try:
                    # 生成客户端ID，格式为：username_client_type_timestamp_random
                    client_id = f"{config['username']}_easytrader_ths_{int(time.time())}_{random.randint(1000, 9999)}"

                    heartbeat_data = {
                        "username": config["username"],
                        "client_type": "easytrader_ths",
                        "client_id": client_id,
                        "timestamp": int(time.time())
                    }
                    logger.info("发送初始心跳包...")
                    sio.emit('heartbeat', heartbeat_data, namespace='/trade')
                    logger.info("初始心跳包发送成功")
                except Exception as e:
                    logger.error(f"发送初始心跳包失败: {e}")
                return True
            logger.debug(f"等待Socket.IO连接... {i+1}/20 秒")
            time.sleep(1)

        if not socket_connected:
            logger.warning("Socket.IO连接超时，20秒内未收到连接成功事件")
            # 尝试重新获取端口
            config["socket_port"] = None
            return False

        return True
    except Exception as e:
        logger.error(f"连接Socket.IO服务器失败: {str(e)}")
        return False

def register_to_backend():
    """向后端注册本客户端"""
    # 确保已登录并获取token
    global auth_token
    if not auth_token and not login_to_backend():
        logger.error("未能获取认证token，无法注册客户端")
        return False

    # 通过Socket.IO连接和注册
    if connect_to_socketio():
        logger.info("已通过Socket.IO连接到后端")
        return True

    logger.error("无法通过Socket.IO连接到后端")
    return False

def heartbeat_thread():
    """定时向后端发送心跳"""
    while True:
        try:
            # 确保有认证token
            global auth_token
            if not auth_token:
                logger.warning("没有认证token，尝试登录")
                if not login_to_backend():
                    logger.error("登录失败，无法发送心跳")
                    time.sleep(config["heartbeat_interval"])
                    continue

            # 确保Socket.IO已连接
            if not socket_connected:
                logger.warning("Socket.IO未连接，尝试重新连接")
                if not connect_to_socketio():
                    logger.error("Socket.IO连接失败，无法发送心跳")
                    time.sleep(config["heartbeat_interval"])
                    continue

            # 发送心跳
            try:
                # 使用固定的客户端ID格式：username_easytrader_ths_client
                client_id = f"{config['username']}_easytrader_ths_client"

                heartbeat_data = {
                    "username": config["username"],
                    "client_type": "easytrader_ths",
                    "client_id": client_id,
                    "timestamp": int(time.time())
                }

                logger.info("Socket.IO连接状态正常，测试心跳发送成功")
                # 在 /trade 命名空间中发送心跳消息
                sio.emit('heartbeat', heartbeat_data, namespace='/trade')
            except Exception as e:
                logger.error(f"发送心跳失败: {str(e)}")
                # 如果发送失败，尝试重新连接
                if not socket_connected:
                    connect_to_socketio()

        except Exception as e:
            logger.error(f"心跳线程发生错误: {str(e)}")

        time.sleep(config["heartbeat_interval"])

def get_local_ip():
    """获取本机IP地址"""
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

# API路由
@app.route('/api/balance', methods=['GET'])
def get_balance():
    """获取账户余额"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        balance = trader.balance
        return jsonify({"success": True, "data": balance})
    except Exception as e:
        logger.error(f"获取账户余额失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/position', methods=['GET'])
def get_position():
    """获取持仓信息"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        position = trader.position
        return jsonify({"success": True, "data": position})
    except Exception as e:
        logger.error(f"获取持仓信息失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

# 写入任务到orders.csv
# 参数: action(str), params(dict)
def append_order_to_csv(action, params):
    """将新任务追加到orders.csv，状态为pending"""
    # 顺序自增id实现
    next_id = 1
    if os.path.isfile(ORDERS_CSV):
        try:
            with open(ORDERS_CSV, mode="r", newline='', encoding="utf-8") as f:
                reader = csv.DictReader(f)
                id_list = [int(row["id"]) for row in reader if row.get("id") and row["id"].isdigit()]
                if id_list:
                    next_id = max(id_list) + 1
        except Exception as e:
            logger.error(f"[任务队列] 读取orders.csv获取id失败: {e}")
            next_id = 1
    order = {
        "id": str(next_id),
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "action": action,
        "params": json.dumps(params, ensure_ascii=False),
        "status": "pending",
        "executed_at": "",
        "result": ""
    }
    file_exists = os.path.isfile(ORDERS_CSV)
    with open(ORDERS_CSV, mode="a", newline='', encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=ORDERS_CSV_FIELDS)
        if not file_exists:
            writer.writeheader()
        writer.writerow(order)
    logger.info(f"[任务队列] 新任务已写入orders.csv: {order}")
    return order["id"]

# 轮询orders.csv并执行待执行任务
# 仅在交易时段内执行pending/failed指令

def poll_and_execute_orders():
    """轮询orders.csv，查找pending任务并在交易时段内执行，更新状态"""
    while True:
        try:
            if not os.path.isfile(ORDERS_CSV):
                time.sleep(ORDERS_POLL_INTERVAL)
                continue
            # 读取所有任务
            with open(ORDERS_CSV, mode="r", newline='', encoding="utf-8") as f:
                reader = list(csv.DictReader(f))
            
            updated = False
            pending_tasks = [row for row in reader if row["status"] == "pending"]
            
            # 只有在有待执行任务时才显示日志
            if pending_tasks:
                logger.info(f"[任务队列] 读取到 {len(reader)} 个任务，其中 {len(pending_tasks)} 个待执行")
                logger.debug(f"[任务队列] 所有任务ID: {[row['id'] for row in reader]}")
            
            for row in reader:
                if row["status"] == "pending":
                    params = row["params"]
                    symbol = extract_symbol_from_params(params)
                    
                    if not symbol:
                        logger.warning(f"[任务队列] 任务 id={row['id']} 无法提取交易品种，标记为失败")
                        row["status"] = "failed"
                        row["executed_at"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        row["result"] = json.dumps({"error": "无法提取交易品种"}, ensure_ascii=False)
                        updated = True
                        continue
                    
                    # 检查是否在交易时间
                    in_trading_time = is_in_trading_time(symbol, commodity_config)
                    
                    if not in_trading_time:
                        # 不在交易时间，静默跳过，不显示日志
                        continue
                    
                    # 只有在交易时间才显示详细日志
                    logger.info(f"[任务队列] 检查任务 id={row['id']}, status={row['status']}, symbol={symbol}")
                    logger.info(f"[任务队列] 任务 id={row['id']} 品种 {symbol} 交易时间检查: {in_trading_time}")
                    
                    try:
                        action = row["action"]
                        params_obj = json.loads(params) if isinstance(params, str) else params
                        logger.info(f"[任务队列] 执行任务: id={row['id']} action={action} params={params_obj}")
                        
                        result = execute_command(action, params_obj)
                        
                        # 检查执行结果是否包含错误信息
                        if isinstance(result, dict) and result.get("error"):
                            logger.warning(f"[任务队列] 任务执行返回错误: id={row['id']} error={result['error']}")
                            row["status"] = "failed"
                            row["executed_at"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            row["result"] = json.dumps(result, ensure_ascii=False)
                        else:
                            row["status"] = "success"
                            row["executed_at"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            row["result"] = json.dumps(result, ensure_ascii=False)
                            logger.info(f"[任务队列] 任务执行成功: id={row['id']}")
                        
                        updated = True
                        
                    except Exception as e:
                        logger.error(f"[任务队列] 任务执行失败: id={row['id']} error={e}")
                        row["status"] = "failed"
                        row["executed_at"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        row["result"] = json.dumps({"error": str(e)}, ensure_ascii=False)
                        updated = True
            
            # 如有更新，写回csv
            if updated:
                try:
                    # 重新读取文件以确保获取最新数据
                    with open(ORDERS_CSV, mode="r", newline='', encoding="utf-8") as f:
                        all_reader = list(csv.DictReader(f))
                    
                    # 更新reader中的对应记录
                    for i, row in enumerate(all_reader):
                        for updated_row in reader:
                            if row['id'] == updated_row['id']:
                                all_reader[i] = updated_row
                                break
                    
                    with open(ORDERS_CSV, mode="w", newline='', encoding="utf-8") as f:
                        writer = csv.DictWriter(f, fieldnames=ORDERS_CSV_FIELDS)
                        writer.writeheader()
                        writer.writerows(all_reader)
                    logger.info(f"[任务队列] 成功更新CSV文件，更新前记录数: {len(reader)}, 更新后记录数: {len(all_reader)}")
                except Exception as e:
                    logger.error(f"[任务队列] 更新CSV文件失败: {e}")
                
        except Exception as e:
            logger.error(f"[任务队列] 轮询执行异常: {e}")
        time.sleep(ORDERS_POLL_INTERVAL)

# 2. Flask接口 /api/buy /api/sell /api/cancel
@app.route('/api/buy', methods=['POST'])
def buy():
    try:
        data = request.get_json(silent=True)
        if data is None:
            return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400
        code = data.get('code')
        price = data.get('price')
        amount = data.get('amount')
        if not all([code, price is not None, amount is not None]):
            return jsonify({"success": False, "message": "参数不完整 (code, price, amount)"}), 400
        order_id = append_order_to_csv('buy', {'code': code, 'price': price, 'amount': amount})
        return jsonify({"success": True, "order_id": order_id})
    except Exception as e:
        logger.error(f"买入股票失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/sell', methods=['POST'])
def sell():
    try:
        data = request.get_json(silent=True)
        if data is None:
            return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400
        code = data.get('code')
        price = data.get('price')
        amount = data.get('amount')
        if not all([code, price is not None, amount is not None]):
            return jsonify({"success": False, "message": "参数不完整 (code, price, amount)"}), 400
        order_id = append_order_to_csv('sell', {'code': code, 'price': price, 'amount': amount})
        return jsonify({"success": True, "order_id": order_id})
    except Exception as e:
        logger.error(f"卖出股票失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/cancel', methods=['POST'])
def cancel_order():
    try:
        data = request.get_json(silent=True)
        if data is None:
            return jsonify({"success": False, "message": "请求体不是有效的JSON或不存在"}), 400
        entrust_no_str = data.get('entrust_no')
        if not entrust_no_str:
            return jsonify({"success": False, "message": "参数不完整 (entrust_no)"}), 400
        order_id = append_order_to_csv('cancel', {'entrust_no': entrust_no_str})
        return jsonify({"success": True, "order_id": order_id})
    except Exception as e:
        logger.error(f"撤销订单失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/today_entrusts', methods=['GET'])
def get_today_entrusts():
    """获取今日委托"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        entrusts = trader.today_entrusts
        return jsonify({"success": True, "data": entrusts})
    except Exception as e:
        logger.error(f"获取今日委托失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/today_trades', methods=['GET'])
def get_today_trades():
    """获取今日成交"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        trades = trader.today_trades
        return jsonify({"success": True, "data": trades})
    except Exception as e:
        logger.error(f"获取今日成交失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/refresh', methods=['POST'])
def refresh():
    """刷新接口"""
    try:
        if trader is None:
            return jsonify({"success": False, "message": "交易接口未初始化"}), 500

        trader.refresh()
        return jsonify({"success": True, "message": "刷新成功"})
    except Exception as e:
        logger.error(f"刷新接口失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """获取客户端状态"""
    try:
        status = {
            "trader_initialized": trader is not None,
            "username": config["username"],
            "local_ip": get_local_ip(),
            "local_port": config["local_port"],
            "backend_url": config["backend_url"],
            "timestamp": int(time.time())
        }

        if trader is not None:
            try:
                # 尝试获取账户信息，检查交易接口是否正常
                balance = trader.balance
                status["trader_status"] = "connected"
            except Exception:
                status["trader_status"] = "error"
        else:
            status["trader_status"] = "not_initialized"

        return jsonify({"success": True, "data": status})
    except Exception as e:
        logger.error(f"获取状态失败: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康状态检查接口，提供详细的健康状态信息"""
    try:
        import psutil
        import platform
        from datetime import datetime

        # 基本状态信息
        health_data = {
            "status": "healthy",  # 默认为健康状态
            "timestamp": int(time.time()),
            "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "uptime": int(time.time() - psutil.boot_time()),
            "client_info": {
            "username": config["username"],
                "local_ip": get_local_ip(),
                "local_port": config["local_port"],
                "backend_url": config["backend_url"],
                "version": "1.0.0"  # 客户端版本
            },
            "system_info": {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "cpu_count": psutil.cpu_count(),
                "memory_total": psutil.virtual_memory().total,
                "memory_available": psutil.virtual_memory().available
            },
            "resource_usage": {
                "cpu_percent": psutil.cpu_percent(interval=0.1),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent
            }
        }

        # 交易接口状态
        trader_status = {
            "initialized": trader is not None,
            "status": "unknown"
        }

        if trader is not None:
            try:
                # 尝试获取账户信息，检查交易接口是否正常
                balance = trader.balance
                trader_status["status"] = "connected"
                trader_status["last_balance_check"] = int(time.time())
                trader_status["balance_check_result"] = "success"

                # 尝试获取持仓信息
                try:
                    position = trader.position
                    trader_status["position_check"] = "success"
                except Exception as pos_e:
                    trader_status["position_check"] = "failed"
                    trader_status["position_error"] = str(pos_e)
            except Exception as e:
                trader_status["status"] = "error"
                trader_status["error"] = str(e)
                health_data["status"] = "warning"  # 交易接口异常，健康状态降级为警告
        else:
            trader_status["status"] = "not_initialized"
            health_data["status"] = "warning"  # 交易接口未初始化，健康状态降级为警告

        health_data["trader"] = trader_status

        # Socket.IO连接状态
        socketio_status = {
            "connected": socket_connected,
            "port": config.get("socket_port"),
            "last_heartbeat": None  # 可以在心跳线程中记录最后一次心跳时间
        }

        if not socket_connected:
            health_data["status"] = "warning"  # Socket.IO未连接，健康状态降级为警告
            socketio_status["reconnect_attempts"] = 0  # 可以记录重连尝试次数

        health_data["socketio"] = socketio_status

        # 检查是否有严重问题
        critical_issues = []

        # 检查CPU使用率是否过高
        if health_data["resource_usage"]["cpu_percent"] > 90:
            critical_issues.append("CPU使用率过高")

        # 检查内存使用率是否过高
        if health_data["resource_usage"]["memory_percent"] > 90:
            critical_issues.append("内存使用率过高")

        # 检查磁盘使用率是否过高
        if health_data["resource_usage"]["disk_usage"] > 90:
            critical_issues.append("磁盘使用率过高")

        # 如果交易接口状态为error，添加到严重问题列表
        if trader_status["status"] == "error":
            critical_issues.append(f"交易接口异常: {trader_status.get('error', '未知错误')}")

        # 如果有严重问题，健康状态降级为不健康
        if critical_issues:
            health_data["status"] = "unhealthy"
            health_data["critical_issues"] = critical_issues

        # 记录健康检查日志
        logger.info(f"健康检查: 状态={health_data['status']}, 交易接口={trader_status['status']}, Socket.IO={socketio_status['connected']}")

        return jsonify({"success": True, "data": health_data})
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": str(e),
            "data": {
                "status": "error",
                "timestamp": int(time.time()),
                "error": str(e)
            }
        }), 500

def login_to_backend():
    global config, auth_token, user_password

    # 如果已有token且不为空，直接返回
    if auth_token:
        logger.info("已有认证token，无需重新登录")
        return True

    # 提示用户输入登录信息
    print("\n===== 登录到量化平台 =====")

    # 如果配置中没有后端URL，提示输入
    if not config.get("backend_url"):
        backend_url = input("请输入后端服务器地址 (例如: http://example.com:3000): ")
        config["backend_url"] = backend_url.strip()
    else:
        print(f"后端服务器地址: {config['backend_url']}")
        change = input("是否修改后端地址? (y/n): ").lower()
        if change == 'y':
            backend_url = input("请输入新的后端服务器地址: ")
            config["backend_url"] = backend_url.strip()

    # 如果配置中没有用户名，提示输入
    if not config.get("username"):
        username = input("请输入用户名: ")
        config["username"] = username.strip()
    else:
        print(f"用户名: {config['username']}")
        change = input("是否修改用户名? (y/n): ").lower()
        if change == 'y':
            username = input("请输入新的用户名: ")
            config["username"] = username.strip()

    # 只在user_password为空时交互式输入密码，否则直接用内存中的
    if not user_password:
        user_password = getpass.getpass("请输入密码: ")

    # 尝试登录
    try:
        # 构建登录API URL
        # 检查 backend_url 是否已经包含 /api/trade 路径
        backend_url = config['backend_url']
        # 移除末尾的斜杠（如果有）
        if backend_url.endswith('/'):
            backend_url = backend_url[:-1]

        # 如果 backend_url 已经包含 /api/trade，则直接使用基础 URL
        if '/api/trade' in backend_url:
            # 提取基础 URL（去掉 /api/trade 部分）
            base_url = backend_url.split('/api/trade')[0]
            login_url = f"{base_url}/api/user/login"
        else:
            # 否则直接添加 /api/user/login
            login_url = f"{backend_url}/api/user/login"

        if not login_url.startswith(("http://", "https://")):
            login_url = f"http://{login_url}"

        logger.info(f"尝试登录到: {login_url}")

        # 发送登录请求
        response = requests.post(
            login_url,
            json={
                "username": config["username"],
                "password": user_password
            },
            timeout=10
        )

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            # 打印响应内容（不包含敏感信息）以便调试
            logger.info(f"登录响应: {result}")

            # 检查响应格式，支持两种可能的格式：
            # 1. { success: true, token: "..." }
            # 2. { success: true, data: { token: "..." } }
            if result.get("success"):
                # 检查 token 是在根级别还是在 data 对象中
                if result.get("token"):
                    auth_token = result["token"]
                    logger.info("登录成功，已获取认证token")
                    return True
                elif result.get("data") and result["data"].get("token"):
                    auth_token = result["data"]["token"]
                    logger.info("登录成功，已获取认证token")
                    return True
                else:
                    logger.error("登录响应中未找到token字段")
                    print("登录响应中未找到token字段")
            else:
                error_msg = result.get('error') or result.get('message') or '未知错误'
                logger.error(f"登录失败: {error_msg}")
                print(f"登录失败: {error_msg}")
        else:
            logger.error(f"登录请求失败，状态码: {response.status_code}")
            print(f"登录请求失败，状态码: {response.status_code}")

        return False
    except Exception as e:
        logger.error(f"登录过程中发生错误: {str(e)}")
        print(f"登录过程中发生错误: {str(e)}")
        return False

def save_config():
    """保存配置到文件 - 只保存必要的配置项"""
    config_path = "easytrader_ths_config.json"
    try:
        # 只保存需要持久化的配置项
        config_to_save = {
            "username": config.get("username", ""),
            "backend_url": config.get("backend_url", ""),
            "ths_path": config.get("ths_path", ""),
            "tesseract_cmd": config.get("tesseract_cmd", "") # 新增 OCR 路径
        }

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_to_save, f, indent=4)
            logger.debug(f"已保存必要配置到文件: {config_path}")
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {str(e)}")
        return False

def load_config():
    """加载配置文件"""
    global config
    config_path = "easytrader_ths_config.json"

    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
                config.update(loaded_config)
                logger.info(f"已加载配置文件: {config_path}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
    else:
        logger.warning(f"配置文件不存在: {config_path}，将使用默认配置")
        # 保存默认配置
        save_config()

def parse_arguments():
    """解析命令行参数"""
    global config
    parser = argparse.ArgumentParser(description='EasyTrader同花顺客户端')
    parser.add_argument('--username', help='用户名')
    parser.add_argument('--backend-url', help='后端URL')
    parser.add_argument('--local-port', type=int, help='本地端口')
    parser.add_argument('--ths-path', help='同花顺客户端路径')

    args = parser.parse_args()

    # 更新配置
    if args.username:
        config["username"] = args.username
    if args.backend_url:
        config["backend_url"] = args.backend_url
    if args.local_port:
        config["local_port"] = args.local_port
    if args.ths_path:
        config["ths_path"] = args.ths_path

def prompt_for_ths_path():
    """提示用户输入同花顺客户端路径和 OCR 路径，仅做路径输入、确认和保存"""
    print("\n===== 同花顺客户端配置 =====")

    # 处理同花顺路径
    if not config.get("ths_path"):
        ths_path = input("请输入同花顺客户端路径: ")
        config["ths_path"] = ths_path.strip()
    else:
        print(f"当前同花顺客户端路径: {config['ths_path']}")
        change = input("是否修改同花顺客户端路径? (y/n): ").lower()
        if change == 'y':
            ths_path = input("请输入新的同花顺客户端路径: ")
            config["ths_path"] = ths_path.strip()

    # 处理 OCR 路径
    if not config.get("tesseract_cmd"):
        tesseract_cmd = input("请输入 OCR 软件(tesseract.exe)路径: ")
        config["tesseract_cmd"] = tesseract_cmd.strip()
    else:
        print(f"当前 OCR 软件路径: {config['tesseract_cmd']}")
        change = input("是否修改 OCR 软件路径? (y/n): ").lower()
        if change == 'y':
            tesseract_cmd = input("请输入新的 OCR 软件(tesseract.exe)路径: ")
            config["tesseract_cmd"] = tesseract_cmd.strip()

    # 保存配置（只保存这两个路径）
    config_path = "easytrader_ths_config.json"
    try:
        config_to_save = {
            "ths_path": config.get("ths_path", ""),
            "tesseract_cmd": config.get("tesseract_cmd", "")
        }
        with open(config_path, 'w', encoding='utf-8') as f:
            import json
            json.dump(config_to_save, f, indent=4)
            logger.debug(f"已保存同花顺和OCR路径到配置文件: {config_path}")
    except Exception as e:
        logger.error(f"保存路径配置文件失败: {str(e)}")
    return bool(config["ths_path"]) and bool(config["tesseract_cmd"])

def prompt_for_local_port():
    """本地服务及依赖配置：端口、同花顺路径、OCR路径"""
    print("\n===== 本地服务配置 =====")

    # 配置本地服务端口
    if not config.get("local_port"):
        try:
            port_str = input(f"请输入本地服务端口 (默认: 8888): ")
            if port_str.strip():
                config["local_port"] = int(port_str.strip())
            else:
                config["local_port"] = 8888
        except ValueError:
            print("端口必须是数字，使用默认端口8888")
            config["local_port"] = 8888
    else:
        print(f"当前本地服务端口: {config['local_port']}")
        change = input("是否修改本地服务端口? (y/n): ").lower()
        if change == 'y':
            try:
                port_str = input("请输入新的本地服务端口: ")
                if port_str.strip():
                    config["local_port"] = int(port_str.strip())
            except ValueError:
                print("端口必须是数字，保持原端口不变")
    save_config()

    # 配置同花顺路径
    if not config.get("ths_path"):
        ths_path = input("请输入同花顺客户端路径: ")
        config["ths_path"] = ths_path.strip()
        save_config()
    else:
        print(f"当前同花顺客户端路径: {config['ths_path']}")
        change = input("是否修改同花顺客户端路径? (y/n): ").lower()
        if change == 'y':
            ths_path = input("请输入新的同花顺客户端路径: ")
            config["ths_path"] = ths_path.strip()
            save_config()

    # 配置 OCR 路径
    if not config.get("tesseract_cmd"):
        tesseract_cmd = input("请输入 OCR 软件(tesseract.exe)路径: ")
        config["tesseract_cmd"] = tesseract_cmd.strip()
        save_config()
    else:
        print(f"当前 OCR 软件路径: {config['tesseract_cmd']}")
        change = input("是否修改 OCR 软件路径? (y/n): ").lower()
        if change == 'y':
            tesseract_cmd = input("请输入新的 OCR 软件(tesseract.exe)路径: ")
            config["tesseract_cmd"] = tesseract_cmd.strip()
            save_config()

    return True

def main():
    """主函数"""
    # 加载配置
    load_config()
    parse_arguments()

    print("\n===== 同花顺交易客户端 =====")
    print("该程序将连接同花顺客户端并与量化平台后端通信")

    # 登录到后端获取token
    global auth_token
    # 强制每次启动都要求登录
    auth_token = ""
    if not login_to_backend():
        logger.error("登录失败，程序退出")
        return

    # 提示输入同花顺客户端路径
    if not config.get("ths_path") and not prompt_for_ths_path():
        logger.error("未指定同花顺客户端路径，程序退出")
        return

    # 提示输入本地端口
    prompt_for_local_port()

    print("\n===== 初始化交易接口 =====")
    # 初始化交易接口
    if not init_trader():
        logger.error("初始化交易接口失败，程序退出")
        return

    print("\n===== 连接到后端服务器 =====")
    # 获取Socket.IO端口并连接
    if not get_socket_port():
        logger.error("获取Socket.IO端口失败，程序退出")
        print("错误: 获取Socket.IO端口失败，程序退出")
        return

    # 注册到后端
    if not register_to_backend():
        logger.warning("注册到后端失败，将继续运行但可能无法被后端发现")
        print("警告: 注册到后端失败，将继续运行但可能无法被后端发现")
        return

    # 启动心跳线程
    hb_thread = threading.Thread(target=heartbeat_thread, daemon=True)
    hb_thread.start()

    # 启动任务轮询线程
    order_thread = threading.Thread(target=poll_and_execute_orders, daemon=True)
    order_thread.start()

    # 启动API服务
    print(f"\n===== 启动本地API服务 =====")
    print(f"本地API服务已启动，监听端口: {config['local_port']}")
    print("现在可以通过量化平台使用同花顺交易功能")
    print("请保持此窗口开启，关闭窗口将断开与后端的连接")

    logger.info(f"启动API服务，监听端口: {config['local_port']}")
    try:
        app.run(host='0.0.0.0', port=config['local_port'])
    except Exception as flask_e:
        logger.error(f"启动 Flask 服务失败: {flask_e}")
        print(f"错误: 启动本地服务失败: {flask_e}")

    # 如果Flask服务器停止，确保Socket.IO也断开连接
    if socket_connected:
        logger.info("断开Socket.IO连接")
        sio.disconnect()

    # 释放Socket.IO端口
    if config.get("socket_port"):
        logger.info(f"释放Socket.IO端口: {config['socket_port']}")
        release_socket_port()

if __name__ == "__main__":
    main()
